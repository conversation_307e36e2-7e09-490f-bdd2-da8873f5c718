import { FastifyRequest } from 'fastify';
import { getApmUserAgents } from './getApmUserAgent';
import { ApmUserAgentsType } from './requestParams.interface';

function createMockRequest(headers = {}): FastifyRequest {
  return {
    raw: { headers }
  } as FastifyRequest;
}

describe('getApmUserAgents', () => {
  it('should return queryUserAgent when queryUserAgent is provided', () => {
    const req = createMockRequest();
    const result = getApmUserAgents(req, 'test-user-agent');

    expect(result).toStrictEqual<ApmUserAgentsType>({
      selectedUA: {
        userAgent: 'test-user-agent',
        uaSource: 'query-param-ua'
      },
      userAgentSources: {
        'query-param-ua': 'test-user-agent',
        'header-x-device-user-agent': 'empty',
        'header-user-agent': 'empty'
      }
    });
  });

  it('should return x-device-user-agent when provided and queryUserAgent is not provided', () => {
    const req = createMockRequest({ 'x-device-user-agent': 'test-device-user-agent' });
    const result = getApmUserAgents(req);

    expect(result).toStrictEqual<ApmUserAgentsType>({
      selectedUA: {
        userAgent: 'test-device-user-agent',
        uaSource: 'header-x-device-user-agent'
      },
      userAgentSources: {
        'query-param-ua': 'empty',
        'header-x-device-user-agent': 'test-device-user-agent',
        'header-user-agent': 'empty'
      }
    });
  });

  it('should return user-agent when provided and queryUserAgent and x-device-user-agent are not provided', () => {
    const req = createMockRequest({ 'user-agent': 'test-user-agent' });
    const result = getApmUserAgents(req);

    expect(result).toStrictEqual<ApmUserAgentsType>({
      selectedUA: {
        userAgent: 'test-user-agent',
        uaSource: 'header-user-agent'
      },
      userAgentSources: {
        'query-param-ua': 'empty',
        'header-x-device-user-agent': 'empty',
        'header-user-agent': 'test-user-agent'
      }
    });
  });

  it('should return empty when no user agent is provided', () => {
    const req = createMockRequest();
    const result = getApmUserAgents(req);

    expect(result).toStrictEqual<ApmUserAgentsType>({
      selectedUA: {
        userAgent: 'empty',
        uaSource: 'empty'
      },
      userAgentSources: {
        'query-param-ua': 'empty',
        'header-x-device-user-agent': 'empty',
        'header-user-agent': 'empty'
      }
    });
  });

  it('should handle priority of headers correctly', () => {
    const req = createMockRequest({
      'x-device-user-agent': 'test-device-user-agent',
      'user-agent': 'test-user-agent'
    });
  });
});
