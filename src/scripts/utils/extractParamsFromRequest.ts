import { LogLevel } from 'adpod-tools';
import { FastifyRequest } from 'fastify';
import logger from '../../libs/logging/logger';
import { getIps } from './getIp';
import { getApmUserAgents } from './getApmUserAgent';
import { IpAndUaSources, RequestDetails, SelectedValues } from './requestParams.interface';

export function extractParamsFromRequest(
  req: FastifyRequest,
  queryIp?: string,
  queryUa?: string
): RequestDetails {
  const { raw } = req;

  const { selectedIps, ipSources } = getIps(req, queryIp);
  const { selectedUA, userAgentSources } = getApmUserAgents(req, queryUa);

  const selectedValues: SelectedValues = {
    ...selectedIps,
    ...selectedUA
  };

  const logData: IpAndUaSources = {
    ipSources,
    userAgentSources,
    selectedValues
  };

  logger('REQUEST_IP_UA', { ...logData }, LogLevel.info);

  const requestDetails: RequestDetails = {
    ...selectedValues,
    rawHeaders: raw.headers,
    requestUrl: raw?.url
  };

  return requestDetails;
}

// TODO: adjust tests. fix also
