import { isValidDateTime } from './isValidDateTime';

describe('isValidDateTime test suite', () => {
  const helperMock = {
    error: () => false
  };
  test('is a function', () => {
    expect(typeof isValidDateTime).toBe('function');
  });
  test('validate, should return true, valid startDate', () => {
    expect(isValidDateTime('2022-04-12T12:00:00+00:00', helperMock)).toEqual(
      '2022-04-12T12:00:00+00:00'
    );
  });
  test('validate, should return false, invalid startDate', () => {
    expect(isValidDateTime('2022-04-12T12:00:00+000:0', helperMock)).toEqual(false);
  });
});
