import { Module } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';
import { ScheduleModule } from '@nestjs/schedule';
import { join } from 'path';
import { CacheModule } from './libs/caching/cache.module';
import { InfoModule } from './components/info/info.module';
import { WhatsonModule } from './components/whatson/whatson.module';
import { PlaylistMultipleModule } from './components/playlist-multiple/playlistMultiple.module';
import { PlaylistSingleModule } from './components/playlist-single/playlistSingle.module';
import { UpdateModule } from './updater/update.module';
import { WorkerConfigModule } from './components/workerConfig/workerConfig.module';
import { DurationBasedPlaylistSingleModule } from './components/duration-based-playlist-single/durationBasedPlaylistSingle.module';
import { AwsS3Module, RedisModule } from 'adpod-aws';
import { validators } from './EnvValidation/envalidConfig';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public')
    }),
    ScheduleModule.forRoot(),
    AwsS3Module.register({
      region: validators.S3_BUCKET_REGION,
      bucketName: validators.S3_BUCKET_NAME,
      roleArn: validators.ROLE_ARN,
      roleSessionName: validators.ROLE_SESSION_NAME,
      externalId: validators.ROLE_EXTERNAL_ID,
      durationSeconds: validators.ROLE_DURATION_SECONDS,
      endpoint: validators.S3_ENDPOINT,
      global: true
    }),
    RedisModule.register({
      host: validators.REDIS_HOST,
      password: validators.REDIS_PASS
    }),
    CacheModule,
    InfoModule,
    PlaylistMultipleModule,
    PlaylistSingleModule,
    DurationBasedPlaylistSingleModule,
    WhatsonModule,
    UpdateModule,
    WorkerConfigModule
  ]
})
export class AppModule {}
