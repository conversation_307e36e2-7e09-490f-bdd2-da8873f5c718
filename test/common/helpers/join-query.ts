export const joinQueryParams = (object?: Record<string, any>): string => {
  const queryStringParams: string[] = [];
  if (!object) return '';

  for (const [key, value] of Object.entries(object)) {
    if (Array.isArray(value)) {
      for (const item of value) {
        if (value !== undefined) {
          queryStringParams.push(`${key}=${item}`);
        }
      }
    } else if (value !== undefined) {
      queryStringParams.push(`${key}=${value}`);
    }
  }
  return `?${queryStringParams.join('&')}`;
};
