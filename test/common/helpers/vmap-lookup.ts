import { VmapNormalized } from '../../../src/interfaces';
import { DebugDetails } from '../../../src/libs';

export class VmapLookup {
  constructor(private readonly vmap: VmapNormalized) {}

  getDebug() {
    const adBreak = this.getAdBreak();
    return adBreak[0].Debug;
  }

  getAdServerDetails() {
    const debug = this.getDebug();

    return (debug?.AdServerDetails?._text && JSON.parse(debug.AdServerDetails._text)) as
      | DebugDetails
      | undefined;
  }

  getAdSlots() {
    return this.getAdBreak()[0]['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad;
  }

  private getAdBreak() {
    const adBreak = this.vmap['vmap:VMAP']['vmap:AdBreak'];
    return Array.isArray(adBreak) ? adBreak : [adBreak];
  }
}
