/* eslint-disable */
import { E2eTestEnvironment } from './e2e-test-environment';
import { ContextProvider } from './context-provider';
import { E2eTestModule, OnTestModuleInit } from './e2e-test.module';
import { TestScenarioBuilder } from './types/builder';

type Module = typeof E2eTestModule<any>;

export function createTestBuilder<U extends Array<Module>>(
  environment: E2eTestEnvironment,
  modules: readonly [...U] | [...U]
): TestScenarioBuilder<U> {
  const context = new ContextProvider();

  const moduleInstances = modules.map((M) => new M(environment, context));

  let lastResult = Promise.resolve({});

  const builder = new Proxy(
    {},
    {
      get(target, name) {
        if (name === 'then' || name === 'catch' || name === 'finally') {
          return (...args: [any]) => {
            const mergedResult = lastResult.then((previous) => ({
              ...context.get(),
              ...previous
            }));

            const returnedValue = mergedResult[name].apply(mergedResult, args);

            lastResult = Promise.resolve({});

            return returnedValue;
          };
        }

        const handlingModule = moduleInstances.find((m) => name in m) || (target as any);
        const property = handlingModule[name];

        if (typeof property !== 'function') {
          return property;
        }

        const handler = (...args: any[]) => {
          lastResult = lastResult.then((previous) =>
            Promise.resolve(property.apply(handlingModule, args)).then((current) =>
              current ? { ...previous, ...current } : previous
            )
          );

          return builder;
        };

        return handler;
      }
    }
  );

  beforeEach(async () => {
    context.clear();

    for (const instance of moduleInstances) {
      if (isInitModule(instance)) {
        await instance.onModuleInit();
      }
    }
  });

  return builder as TestScenarioBuilder<U>;
}

function isInitModule(module: unknown): module is OnTestModuleInit {
  return (module as OnTestModuleInit)?.onModuleInit instanceof Function;
}
