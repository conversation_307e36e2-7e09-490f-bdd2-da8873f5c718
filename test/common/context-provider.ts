export class ContextProvider<TContext> {
  private context: Partial<TContext>;

  constructor() {
    this.context = {};
  }

  get() {
    return { ...this.context };
  }

  merge<T extends Partial<TContext>>(data: T) {
    Object.assign(this.context, data);

    return data;
  }

  clear(name?: keyof TContext) {
    if (name) {
      this.context = { ...this.context, [name]: undefined };
    } else {
      this.context = {};
    }
  }
}
