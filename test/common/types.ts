import {
  AdError,
  AdImpression,
  AdTracking,
  Extension,
  MediaFile,
  TextType
} from 'adpod-tools';
import { TrackingType, VmapAdBreakNormalized } from '../../src/interfaces';

export type DeepPartial<T> = T extends object ? { [P in keyof T]?: DeepPartial<T[P]> } : T;

export type Vmap = {
  AdSource?: Pick<VmapAdBreakNormalized, 'vmap:AdSource'>;
  Duration?: TextType;
  MediaFile?: MediaFile;
  Impressions?: AdImpression[];
  Errors?: AdError[];
  Extensions?: Extension[];
  TrackingEvents?: TrackingType[];
  TrackingScripts?: AdTracking[];
};
