import { INestApplication, NestApplicationOptions, Type } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import axios, { Axios, AxiosError } from 'axios';
import { Server } from 'http';
import { AddressInfo } from 'net';
import { SetupServer, setupServer } from 'msw/node';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { S3 } from '@aws-sdk/client-s3';
import { validators } from '../../src/EnvValidation/envalidConfig';
import { workerConfigSeed } from '../seeds/worker-config';

export type EnvironmentConfig = {
  rootModule: Type<any>;
  nestApplicationOptions?: NestApplicationOptions;
};

export class E2eTestEnvironment {
  public app: INestApplication;

  public httpServer: Server;

  public httpMockServer: SetupServer;

  public axiosInstance: Axios;

  public s3: S3;

  async init(config: EnvironmentConfig) {
    this.setupHttpMockServer();
    this.setS3Client();
    await this.seedWorkerConfig();
    await this.createNestApp(config);
    await this.startHttpServer();
    this.createAxiosInstance();
  }

  async reset() {
    this.httpMockServer.resetHandlers();
    this.httpMockServer.use();
    await this.deleteBucketObjectsWithExclusion();
  }

  async close() {
    await this.app.close();
    this.httpServer?.close();
    this.httpMockServer?.close();
    this.s3.destroy();
  }

  private createAxiosInstance() {
    const address = this.httpServer.address() as AddressInfo;
    const baseURL = `http://127.0.0.1:${address.port}`;

    this.axiosInstance = axios.create({ baseURL, timeout: 3000 });

    this.axiosInstance.interceptors.response.use(
      (res) => res,
      (err) => {
        this.enhanceErrorResponse(err as AxiosError);

        throw err;
      }
    );
  }

  private setS3Client() {
    this.s3 = new S3({
      region: validators.S3_BUCKET_REGION,
      endpoint: validators.S3_ENDPOINT,
      requestHandler: {
        httpAgent: {
          maxSockets: 250
        },
        socketAcquisitionWarningTimeout: 20 * 1000
      }
    });
  }

  private async startHttpServer() {
    this.httpServer = (await this.app.listen(0)) as Server;
  }

  private async createNestApp({ rootModule, nestApplicationOptions }: EnvironmentConfig) {
    const testingModule = await Test.createTestingModule({
      imports: [rootModule]
    }).compile();

    this.app = testingModule.createNestApplication(
      new FastifyAdapter(),
      nestApplicationOptions
    );

    await this.app.init();
  }

  private setupHttpMockServer() {
    this.httpMockServer = setupServer();
    this.httpMockServer.listen({ onUnhandledRequest: 'bypass' });
  }

  private enhanceErrorResponse(error: AxiosError) {
    const { response } = error;

    if (!response) return;

    error.message = `Request failed with status code ${response.status} ${
      response.statusText
    }. Request URL: ${response.config.url}, response: ${JSON.stringify(response.data)}`;
  }

  private async deleteBucketObjectsWithExclusion() {
    try {
      const excludePattern = /^apm-configs\//;

      const objects = await this.s3.listObjects({
        Bucket: validators.S3_BUCKET_NAME
      });

      const objectsToDelete =
        objects.Contents?.filter(
          (object) => object.Key && !excludePattern.test(object.Key)
        ).map((object) => ({ Key: object.Key })) ?? [];

      if (!objectsToDelete.length) return;

      await this.s3.deleteObjects({
        Bucket: validators.S3_BUCKET_NAME,
        Delete: {
          Objects: objectsToDelete,
          Quiet: false
        }
      });
    } catch (error) {
      console.error('An error occurred during deleting objects from bucket', error);
    }
  }

  private async seedWorkerConfig() {
    await this.s3.putObject({
      Bucket: validators.S3_BUCKET_NAME,
      Key: validators.S3_WORKER_CONFIG_PATH,
      Body: JSON.stringify(workerConfigSeed),
      ContentType: 'application/json'
    });
  }
}

export function setupE2eTestEnvironment(config: EnvironmentConfig) {
  const environment = new E2eTestEnvironment();

  beforeAll(async () => {
    await environment.init(config);
  });

  beforeEach(async () => {
    await environment.reset();
  });

  afterAll(async () => {
    await environment.close();
  });

  return environment;
}
