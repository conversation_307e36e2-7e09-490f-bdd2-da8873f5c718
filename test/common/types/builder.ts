import { E2eContext, E2eTestModule } from '../e2e-test.module';
import { ContextProperty } from './context';

type ModuleConstructor<T> = typeof E2eTestModule<T>;

type Constructor<T = any> = new (...args: any[]) => T;
type ConstructorResult<T> = T extends Constructor<infer U> ? U : never;
type Public<T> = Pick<T, keyof T>;

type MapResultTypes<T extends Constructor[]> = {
  [P in keyof T]: Public<ConstructorResult<T[P]>>;
};

type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never;

type AnyFunction = (...a: any[]) => any;

type UnwrapPromiseResult<T extends AnyFunction> = Awaited<ReturnType<T>>;

type TestModules<T extends Constructor[]> = UnionToIntersection<MapResultTypes<T>[number]>;

type MapFunction<TFn, TR> = TFn extends (...a: infer A) => any ? (...a: A) => TR : never;

type Spread<T, U> = Pick<T, Exclude<keyof T, keyof U>> & U;

type TestScenarioBuilderBase<T extends Array<ModuleConstructor<any>>, U> = {
  [K in keyof TestModules<T>]: TestModules<T>[K] extends AnyFunction
    ? MapFunction<
        TestModules<T>[K],
        TestScenarioBuilder<T, Spread<U, UnwrapPromiseResult<TestModules<T>[K]>>>
      >
    : TestModules<T>[K];
};

export type TestScenarioBuilder<
  T extends Array<ModuleConstructor<any>>,
  U = E2eContext
> = TestScenarioBuilderBase<T, U> & ContextProperty<T> & Promise<U>;
