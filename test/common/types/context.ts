import { ContextProvider } from '../context-provider';
import { E2eContext, E2eTestModule } from '../e2e-test.module';

type ModuleConstructor<T> = typeof E2eTestModule<T>;

type ModuleList = Array<ModuleConstructor<any>>;

type ExtractContextTypes<T extends ModuleList> = {
  [P in keyof T]: T[P] extends ModuleConstructor<infer U> ? U : never;
};
type ExtractContextType<T extends ModuleList> = UnionToIntersection<
  ExtractContextTypes<T>[number]
>;

type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (
  k: infer I
) => void
  ? I
  : never;

type FlattenTypeName<T> = T extends infer U ? { [K in keyof U]: U[K] } : never;

type ContextType<T extends ModuleList> = FlattenTypeName<E2eContext & ExtractContextType<T>>;

export type ContextProperty<T extends ModuleList> = {
  context: ContextProvider<ContextType<T>>;
};
