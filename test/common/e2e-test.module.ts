import { AxiosResponse, RawAxiosRequestHeaders } from 'axios';
import { E2eTestEnvironment } from './e2e-test-environment';
import { DeepPartial } from './types';
import { VmapBuilder } from '../builders/vmap.builder';
import { VastBuilder } from '../builders/vast.builder';
import { ContextProvider } from './context-provider';

type RequestData = string | object;
type Headers = RawAxiosRequestHeaders;

type RequestParams = {
  method: 'get' | 'put' | 'patch' | 'post' | 'delete';
  url: string;
  data?: RequestData;
  headers?: Headers;
};

export type E2eHttpContext = {
  response: AxiosResponse;
};

export type E2eContext = E2eHttpContext;

export type Callback<TContext, TResponse = void> = (
  context?: Partial<MergedContext<TContext>>
) => {
  statusCode?: number;
  body?: DeepPartial<TResponse>;
};

type MergedContext<T> = T & E2eContext;

export interface OnTestModuleInit {
  onModuleInit(): void | Promise<void>;
}

export class E2eTestModule<TContext> {
  public readonly vastBuilder = new VastBuilder();

  public readonly vmapBuilder = new VmapBuilder(this.vastBuilder);

  constructor(
    public readonly environment: E2eTestEnvironment,
    protected readonly context: ContextProvider<MergedContext<TContext>>
  ) {}

  get<TResponse = unknown>(url: string, headers?: Headers) {
    return this.sendRequest<TResponse>({ method: 'get', url, headers });
  }

  post<TResponse = unknown>(url: string, data?: RequestData, headers?: Headers) {
    return this.sendRequest<TResponse>({ method: 'post', url, data, headers });
  }

  put<TResponse = unknown>(url: string, data?: RequestData, headers?: Headers) {
    return this.sendRequest<TResponse>({ method: 'put', url, data, headers });
  }

  patch<TResponse = unknown>(url: string, data?: RequestData, headers?: Headers) {
    return this.sendRequest<TResponse>({ method: 'patch', url, data, headers });
  }

  delete<TResponse = unknown>(url: string, headers?: Headers) {
    return this.sendRequest<TResponse>({ method: 'delete', url, headers });
  }

  private async sendRequest<TResponse = unknown>({
    method,
    url,
    data,
    headers
  }: RequestParams) {
    const response = await this.environment.axiosInstance.request<TResponse>({
      method,
      headers,
      url,
      data
    });

    this.context.merge({ response } as MergedContext<TContext>);

    return response;
  }
}
