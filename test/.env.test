NEW_RELIC_APP_NAME=LOCAL-Ad-Worker
CACHE_SIZE_DAYS=2
CACHE_BUCKET_FETCH_DAYS_LIMIT=7
REFRESH_CACHE_INTERVAL=4
TRACE_HEADERS=x-device-ip,x-device-user-agent
REDIS_CLUSTER_UPDATE_STATUS_RETRY_INTERVAL=600000
CACHE_CONFIGS_TO_SET_LOCALLY=''
AWS_SDK_LOAD_CONFIG=true
AWS_ACCESS_KEY_ID=fake-admin
AWS_SECRET_ACCESS_KEY=fake-secret123
S3_BUCKET_NAME=adpod-test-bucket
S3_BUCKET_REGION=eu-central-1
S3_ENDPOINT=http://s3.local.gd:9000
ROLE_ARN=arn
ROLE_SESSION_NAME=session_name
ROLE_EXTERNAL_ID=external-id
REDIS_HOST=redis.local.gd
REDIS_PASS=bitnami
REDIS_MAX_CONNECTION_RETRYS=100
HEALTH_CHECK_LIMIT=50
BASE_ADO_URL=$HTTP_PROTOCOL://tvn.adocean.pl/ad.xml
SNOWFLAKE_URL=http://snowflake-url
CUST_PARAMS_VALIDATION_EXCLUDES=gdpr_consent
START_BID_S3_FETCH=ENABLED
DEFAULT_PREFETCH_CAP=120
PREFETCH_NEXT_REPLACED_OFFSET=2
NODE_ENV=test
E2E_TESTS=true
APP_NAME=adpod-worker
HOSTNAME=
DD_SERVICE_NAME=Worker
WORKER_CONFIG_INTERVAL_UPDATE_SECONDS=300
GAM_CERT=fake-cert
GAM_KEY=fake-key