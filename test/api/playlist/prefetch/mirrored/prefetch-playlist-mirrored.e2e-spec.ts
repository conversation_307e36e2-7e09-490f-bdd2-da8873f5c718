import { AdType, BreakConnector, Channel } from 'adpod-tools';
import { AppModule } from '../../../../../src/app.module';
import { setupE2eTestEnvironment } from '../../../../common/e2e-test-environment';
import { ConfigurationBuilder } from '../../../../builders/configuration.builder';
import dayjs from 'dayjs';
import { createTestBuilder } from '../../../../common/e2e-test.builder';
import { ConfigurationRequest } from '../../../../requests/configuration.request';
import { PrefetchPlaylistRequest } from '../../../../requests/prefetch.requests';
import { PrefetchType } from '../../../../../src/models/prefetch.model';
import { WorkerConfigRequest } from '../../../../requests/worker-config.request';

describe('Prefetch playlist', () => {
  const environment = setupE2eTestEnvironment({ rootModule: AppModule });

  const requests = [
    ConfigurationRequest,
    ConfigurationBuilder,
    PrefetchPlaylistRequest,
    WorkerConfigRequest
  ] as const;

  const builder = createTestBuilder(environment, requests);

  it('prefetch request should return mirrored ads', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.freeWheelSchedule
      })
      .addAdSlot({
        type: AdType.tv
      })
      .addAdSlotVast()
      .build()
      .uploadConfiguration();

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(prefetchPlaylistLookup.getAdServerDetails()).toMatchObject({
      break: [],
      slots: [],
      finalSlotsReplaced: []
    });
  });
});
