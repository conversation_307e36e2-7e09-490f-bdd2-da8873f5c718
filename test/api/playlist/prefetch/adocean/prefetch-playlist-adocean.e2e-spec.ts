import { AdType, BreakConnector, Channel } from 'adpod-tools';
import { AppModule } from '../../../../../src/app.module';
import { setupE2eTestEnvironment } from '../../../../common/e2e-test-environment';
import { ConfigurationBuilder } from '../../../../builders/configuration.builder';
import { AdOceanResponse, adOceanUrl } from '../../../../servers/adocean.response';
import dayjs from 'dayjs';
import { createTestBuilder } from '../../../../common/e2e-test.builder';
import { ConfigurationRequest } from '../../../../requests/configuration.request';
import { PrefetchPlaylistRequest } from '../../../../requests/prefetch.requests';
import { PrefetchType } from '../../../../../src/models/prefetch.model';
import { WorkerConfigRequest } from '../../../../requests/worker-config.request';

jest.mock('adpod-tools/dist/common/logging/logger')

describe('Prefetch playlist - AdOcean Integration', () => {
  const environment = setupE2eTestEnvironment({ rootModule: AppModule });

  const requests = [
    AdOceanResponse,
    ConfigurationRequest,
    ConfigurationBuilder,
    PrefetchPlaylistRequest,
    WorkerConfigRequest
  ] as const;

  const builder = createTestBuilder(environment, requests);

  it('prefetch request should request AdOcean adserver and process response correctly', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(1.5, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=test123&ch=TVN&bid=test-break-id&ct=linear&mid1dur=15&mid1maxdur=15&mid1mindur=15`,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanResponse({ Duration: { _text: '00:00:15' } }, { once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);
    expect(adOceanRequestUrls![0]).toContain('tvn.adocean.pl');
    expect(adOceanRequestUrls![0]).toContain('aocodetype=1');
    expect(adOceanRequestUrls![0]).toContain('ch=TVN');
    expect(adOceanRequestUrls![0]).toContain('ct=linear');

    const adSlots = prefetchPlaylistLookup.getAdSlots();
    expect(adSlots).toHaveLength(1);

    const adServerDetails = prefetchPlaylistLookup.getAdServerDetails();
    expect(adServerDetails).toMatchObject({
      break: [
        {
          position: null,
          connector: BreakConnector.adoceanBreakSchedule,
          adServerUrl: adOceanRequestUrls![0]
        }
      ],
      slots: [],
      finalSlotsReplaced: [{ position: null, connector: BreakConnector.adoceanBreakSchedule }]
    });
  });

  it('prefetch request should handle AdOcean response with multiple ad durations', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.mtit,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(2, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=test456&ch=MTIT&bid=test-break-id-2&ct=linear&mid1dur=30&mid1maxdur=30&mid1mindur=30`,
        duration: 30
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanResponse(
        {
          Duration: { _text: '00:00:30' },
          AdTitle: { _text: '[Test] AdOcean MTIT Ad 30s' }
        },
        { once: true }
      );

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.mtit,
        cust_params: 'TEST_AUTO=true&ado_id=custom_ado_id',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);
    expect(adOceanRequestUrls![0]).toContain('tvn.adocean.pl');
    expect(adOceanRequestUrls![0]).toContain('ch=MTIT');
    expect(adOceanRequestUrls![0]).toContain('mid1dur=30');

    // Verify custom parameters are processed correctly
    expect(adOceanRequestUrls![0]).toContain('id=custom_ado_id');

    const adSlots = prefetchPlaylistLookup.getAdSlots();
    expect(adSlots).toHaveLength(1);

    // Verify the ad contains the correct duration
    const firstAd = adSlots[0];
    expect(firstAd.InLine?.Creatives?.Creative?.[0]?.Linear?.Duration?._text).toBe('00:00:30');
  });

  it('prefetch request should handle AdOcean empty response gracefully', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(1, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=empty_test&ch=TVN&bid=empty-break-id&ct=linear&mid1dur=15&mid1maxdur=15&mid1mindur=15`,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanEmptyResponse({ once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);

    const adServerDetails = prefetchPlaylistLookup.getAdServerDetails();
    expect(adServerDetails).toMatchObject({
      break: [],
      slots: [],
      finalSlotsReplaced: []
    });
  });

  it('prefetch request should handle AdOcean with GDPR parameters', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(1, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=gdpr_test&ch=TVN&bid=gdpr-break-id&ct=linear&mid1dur=15&gdpr=1&gdpr_consent=test_consent_string`,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanResponse({ Duration: { _text: '00:00:15' } }, { once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);
    expect(adOceanRequestUrls![0]).toContain('gdpr=1');
    expect(adOceanRequestUrls![0]).toContain('gdpr_consent=test_consent_string');

    const adSlots = prefetchPlaylistLookup.getAdSlots();
    expect(adSlots).toHaveLength(1);
  });

  it('prefetch request should handle AdOcean with custom tracking events', async () => {
    // arrange
    const customTrackingEvents = [
      { _attributes: { event: 'start' }, _cdata: 'http://custom-tracking.com/start' },
      { _attributes: { event: 'complete' }, _cdata: 'http://custom-tracking.com/complete' }
    ];

    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(1, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=tracking_test&ch=TVN&bid=tracking-break-id&ct=linear&mid1dur=15`,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanResponse(
        {
          Duration: { _text: '00:00:15' },
          TrackingEvents: customTrackingEvents
        },
        { once: true }
      );

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);

    const adSlots = prefetchPlaylistLookup.getAdSlots();
    expect(adSlots).toHaveLength(1);

    // Verify custom tracking events are preserved
    const trackingEvents =
      adSlots[0].InLine?.Creatives?.Creative?.[0]?.Linear?.TrackingEvents?.Tracking;
    expect(trackingEvents).toBeDefined();
    expect(trackingEvents).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          _attributes: { event: 'start' },
          _cdata: 'http://custom-tracking.com/start'
        }),
        expect.objectContaining({
          _attributes: { event: 'complete' },
          _cdata: 'http://custom-tracking.com/complete'
        })
      ])
    );
  });

  it('prefetch request should handle AdOcean with user ID and IP parameters', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.tvn,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        time: dayjs().add(1, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: `${adOceanUrl}?aocodetype=1&id=user_test&ch=TVN&bid=user-break-id&ct=linear&mid1dur=15`,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockAdOceanResponse({ Duration: { _text: '00:00:15' } }, { once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.tvn,
        cust_params: 'TEST_AUTO=true&uid=test_user_123',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { adOceanRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(adOceanRequestUrls).toHaveLength(1);

    // Verify user ID parameter is included in the request
    const requestUrl = adOceanRequestUrls![0];
    expect(requestUrl).toContain('aouserid=test_user_123');

    const adSlots = prefetchPlaylistLookup.getAdSlots();
    expect(adSlots).toHaveLength(1);
  });
});
