# AdOcean Integration E2E Tests

This directory contains comprehensive end-to-end tests for the AdOcean (ADO) ad service integration, specifically testing the "oneRequest" variant of the `AdOceanBreakDaiAdsProvider`.

## Overview

AdOcean is one of the primary ad servers integrated with the TVN Ad Decisioning System. These tests verify that:

1. **HTTP Requests** are made to correct AdOcean endpoints with proper parameters
2. **Response Processing** correctly parses AdOcean VAST responses
3. **Ad Slot Replacement** logic works correctly for AdOcean ads
4. **Parameter Handling** processes AdOcean-specific parameters (GDPR, user ID, custom params)
5. **Error Scenarios** are handled gracefully (empty responses, timeouts)

## Test Structure

### Files

- `prefetch-playlist-adocean.e2e-spec.ts` - Main test suite
- `../../../servers/adocean.response.ts` - AdOcean response mock server

### Test Environment

The tests use the same infrastructure as other e2e tests:
- **NestJS Test Module** with full application context
- **MSW (Mock Service Worker)** for HTTP mocking
- **Builder Pattern** for test setup and configuration
- **Docker Services** for S3 and Redis dependencies

## Test Scenarios

### 1. Basic AdOcean Integration
```typescript
it('prefetch request should request AdOcean adserver and process response correctly')
```
- Tests successful AdOcean ad request and response processing
- Verifies correct URL parameters (`aocodetype=1`, `ch=TVN`, `ct=linear`)
- Validates VAST response structure and ad slot replacement

### 2. Multiple Ad Durations
```typescript
it('prefetch request should handle AdOcean response with multiple ad durations')
```
- Tests different ad durations (15s, 30s)
- Verifies duration-specific URL parameters (`mid1dur=30`, `mid1maxdur=30`)
- Tests custom parameter processing (`ado_id` parameter handling)

### 3. Empty Response Handling
```typescript
it('prefetch request should handle AdOcean empty response gracefully')
```
- Tests behavior when AdOcean returns empty VAST
- Verifies graceful fallback to mirrored ads
- Ensures no errors are thrown on empty responses

### 4. GDPR Parameter Handling
```typescript
it('prefetch request should handle AdOcean with GDPR parameters')
```
- Tests GDPR consent parameter passing
- Verifies `gdpr=1` and `gdpr_consent` parameters in requests
- Ensures compliance with privacy regulations

### 5. Custom Tracking Events
```typescript
it('prefetch request should handle AdOcean with custom tracking events')
```
- Tests preservation of custom tracking events from AdOcean
- Verifies tracking URLs are correctly maintained
- Tests event-specific parameter handling

### 6. User ID and IP Parameters
```typescript
it('prefetch request should handle AdOcean with user ID and IP parameters')
```
- Tests user identification parameter passing
- Verifies `aouserid` parameter inclusion
- Tests IP address forwarding to AdOcean

## AdOcean URL Structure

AdOcean requests follow this pattern:
```
http://tvn.adocean.pl/ad.xml?aocodetype=1&id={adUnitId}&ch={channel}&bid={breakId}&ct=linear&mid{position}dur={duration}&gdpr={gdpr}&gdpr_consent={consent}
```

### Key Parameters:
- `aocodetype=1` - AdOcean API version
- `id` - Ad unit identifier
- `ch` - TV channel (TVN, MTIT, etc.)
- `bid` - Break identifier
- `ct=linear` - Content type (linear video)
- `mid{N}dur` - Duration for position N
- `gdpr` - GDPR compliance flag
- `gdpr_consent` - GDPR consent string

## Mock Server

The `AdOceanResponse` class provides realistic AdOcean VAST responses:

### Features:
- **Realistic VAST 4.0** structure matching AdOcean format
- **UniversalAdId** with `idRegistry="adocean"`
- **Customizable** duration, tracking events, and media files
- **Error Handling** for empty responses and timeouts

### Usage:
```typescript
.mockAdOceanResponse({ Duration: { _text: '00:00:30' } }, { once: true })
.mockAdOceanEmptyResponse({ once: true })
```

## Running the Tests

### Prerequisites:
1. Docker daemon running
2. Test services started: `npm run pretest:e2e`

### Execute Tests:
```bash
# Run all AdOcean e2e tests
npm run test:e2e -- --testPathPattern="adocean"

# Run specific test
npm run test:e2e -- --testNamePattern="should request AdOcean adserver"

# Run with verbose output
npm run test:e2e -- --testPathPattern="adocean" --verbose
```

## Integration Points

### AdOceanBreakDaiAdsProvider
- Tests the `oneRequest` method specifically
- Verifies break-level ad requests (not slot-level)
- Tests ad slot replacement logic

### URL Generation
- Tests `createADORequestUrl` function
- Verifies parameter encoding and formatting
- Tests custom parameter merging

### Response Processing
- Tests VAST XML parsing
- Verifies `UniversalAdId` extraction for AdOcean ads
- Tests ad duration and metadata extraction

## Best Practices

1. **Use Realistic Data** - Test with actual AdOcean URL patterns
2. **Test Edge Cases** - Empty responses, malformed VAST, network errors
3. **Verify URLs** - Always check generated AdOcean request URLs
4. **Mock Appropriately** - Use `{ once: true }` for single-use mocks
5. **Clean Assertions** - Verify both HTTP requests and response processing

## Troubleshooting

### Common Issues:

1. **Docker Not Running**
   ```
   unable to get image 'quay.io/minio/minio': Cannot connect to the Docker daemon
   ```
   Solution: Start Docker daemon

2. **Mock Not Triggered**
   - Verify AdOcean URL matches exactly
   - Check mock is registered before test execution
   - Ensure `{ once: true }` is used correctly

3. **VAST Parsing Errors**
   - Verify VAST structure matches expected format
   - Check `UniversalAdId` is present for AdOcean ads
   - Ensure required fields are not missing

## Future Enhancements

1. **Recursive Request Testing** - Add tests for `exactLength=true` scenarios
2. **Error Response Testing** - Test HTTP error codes and timeouts
3. **Performance Testing** - Add response time assertions
4. **Multiple Ad Testing** - Test multiple ads in single response
5. **Slot-Level Testing** - Add tests for `AdOceanProxyDaiAdsProvider`
