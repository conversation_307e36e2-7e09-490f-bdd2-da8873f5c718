import { AdType, BreakConnector, Channel } from 'adpod-tools';
import { AppModule } from '../../../../../src/app.module';
import { setupE2eTestEnvironment } from '../../../../common/e2e-test-environment';
import { ConfigurationBuilder } from '../../../../builders/configuration.builder';
import { FreeWheelResponse, freeWheelUrl } from '../../../../servers/freewheel.response';
import dayjs from 'dayjs';
import { createTestBuilder } from '../../../../common/e2e-test.builder';
import { ConfigurationRequest } from '../../../../requests/configuration.request';
import { PrefetchPlaylistRequest } from '../../../../requests/prefetch.requests';
import { PrefetchType } from '../../../../../src/models/prefetch.model';
import { WorkerConfigRequest } from '../../../../requests/worker-config.request';

describe('Prefetch playlist', () => {
  const environment = setupE2eTestEnvironment({ rootModule: AppModule });

  const requests = [
    FreeWheelResponse,
    ConfigurationRequest,
    ConfigurationBuilder,
    PrefetchPlaylistRequest,
    WorkerConfigRequest
  ] as const;

  const builder = createTestBuilder(environment, requests);

  it('prefetch request should request freewheel adserver but mirrored ads are returned', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.mtit,
        breakConnector: BreakConnector.freeWheelSchedule,
        time: dayjs().add(1.5, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: freeWheelUrl,
        duration: 15
      })
      .addAdSlotVast()
      .addAdSlot({
        type: AdType.atv,
        adrequest: freeWheelUrl,
        duration: 20
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockFreeWheelResponse({ Duration: { _text: '00:00:15' } }, { once: true })
      .mockFreeWheelResponse({ Duration: { _text: '00:00:15' } }, { once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.mtit,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { freeWheelRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(freeWheelRequestUrls).toHaveLength(2);
    expect(prefetchPlaylistLookup.getAdSlots()).toHaveLength(2);
    expect(prefetchPlaylistLookup.getAdServerDetails()).toMatchObject({
      break: [],
      slots: [
        [
          {
            position: 1,
            connector: BreakConnector.freeWheelSchedule,
            adServerUrl: freeWheelRequestUrls![0]
          }
        ],
        [
          {
            position: 2,
            connector: BreakConnector.freeWheelSchedule,
            adServerUrl: freeWheelRequestUrls![1]
          }
        ]
      ],
      finalSlotsReplaced: [{ position: 1, connector: BreakConnector.freeWheelSchedule }]
    });
  });

  it('prefetch request should request freewheel adserver and return all slot replaced', async () => {
    // arrange
    await builder
      .addConfiguration({
        channel: Channel.mtit,
        breakConnector: BreakConnector.freeWheelSchedule,
        time: dayjs().add(1.5, 'hours').toISOString(),
        metadata: {
          hasAtvSlot: true
        }
      })
      .addAdSlot({
        type: AdType.atv,
        adrequest: freeWheelUrl,
        duration: 15
      })
      .addAdSlotVast()
      .addAdSlot({
        type: AdType.atv,
        adrequest: freeWheelUrl,
        duration: 15
      })
      .addAdSlotVast()
      .build()
      .uploadConfigurations()
      .mockFreeWheelResponse({ Duration: { _text: '00:00:15' } }, { once: true })
      .mockFreeWheelResponse({ Duration: { _text: '00:00:15' } }, { once: true });

    // act
    const { response, prefetchPlaylist, prefetchPlaylistLookup } =
      await builder.prefetchPlaylist({
        mode: PrefetchType.nextDebugValid,
        ch: Channel.mtit,
        cust_params: 'TEST_AUTO=true',
        startDate: dayjs().subtract(1, 'hour').toISOString()
      });

    // assert
    const { freeWheelRequestUrls } = builder.context.get();

    expect(response.status).toBe(200);
    expect(prefetchPlaylist).toBeDefined();
    expect(freeWheelRequestUrls).toHaveLength(2);
    expect(prefetchPlaylistLookup.getAdSlots()).toHaveLength(2);
    expect(prefetchPlaylistLookup.getAdServerDetails()).toMatchObject({
      break: [],
      slots: [
        [
          {
            position: 1,
            connector: BreakConnector.freeWheelSchedule,
            adServerUrl: freeWheelRequestUrls![0]
          }
        ],
        [
          {
            position: 2,
            connector: BreakConnector.freeWheelSchedule,
            adServerUrl: freeWheelRequestUrls![1]
          }
        ]
      ],
      finalSlotsReplaced: [
        {
          position: 1,
          connector: BreakConnector.freeWheelSchedule
        },
        {
          position: 2,
          connector: BreakConnector.freeWheelSchedule
        }
      ]
    });
  });
});
