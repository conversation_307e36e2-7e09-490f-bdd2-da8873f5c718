/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import { AdImpression, AdTracking, xmlParser } from 'adpod-tools';
import { E2eTestModule } from '../common/e2e-test.module';
import { http, HttpResponse, RequestHandlerOptions } from 'msw';
import { HttpStatus } from '@nestjs/common';
import { faker } from '@faker-js/faker';
import { Vmap } from '../common/types';
import { TrackingType, VmapNormalized } from '../../src/interfaces';
import { VmapBuilder } from '../builders';

export const freeWheelUrl = 'http://freewheel-url.net/ad/g/1';

export type FreeWheelContext = {
  freeWheelResponse: VmapNormalized;
  freeWheelRequestUrls: string[];
};

export class FreeWheelResponse extends E2eTestModule<FreeWheelContext> {
  mockFreeWheelResponse(vmap?: Vmap, options?: RequestHandlerOptions) {
    this.environment.httpMockServer.use(
      http.get(
        freeWheelUrl,
        ({ request }) => {
          try {
            const response = this.prepareResponse(vmap);

            this.context.merge({
              freeWheelResponse: response,
              freeWheelRequestUrls: [
                ...(this.context.get().freeWheelRequestUrls ?? []),
                request.url
              ]
            });

            return HttpResponse.xml(xmlParser.fromJSONtoXML(response));
          } catch (error) {
            return HttpResponse.json({ error }, { status: HttpStatus.INTERNAL_SERVER_ERROR });
          }
        },
        options
      )
    );
  }

  private prepareResponse(vmap?: Vmap) {
    const builder = this.vmapBuilder
      .addVmap()
      .addVastAdSlot()
      .addMediaFile(vmap?.MediaFile)
      .addDuration(vmap?.Duration);

    this.setTrackingScripts(builder, vmap?.TrackingScripts);
    this.setImpressions(builder, vmap?.Impressions);
    this.setTrackingEvents(builder, vmap?.TrackingEvents);

    return builder.build();
  }

  private setTrackingScripts(builder: VmapBuilder, trackingScripts?: AdTracking[]) {
    if (!trackingScripts?.length) {
      builder
        .addTrackingScript({ _attributes: { event: 'complete' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'firstQuartile' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'midpoint' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'thirdQuartile' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'mute' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'unmute' }, _cdata: freeWheelUrl })
        .addTrackingScript({
          _attributes: { event: 'playerCollapse' },
          _cdata: freeWheelUrl
        })
        .addTrackingScript({ _attributes: { event: 'playerExpand' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'pause' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'resume' }, _cdata: freeWheelUrl })
        .addTrackingScript({ _attributes: { event: 'rewind' }, _cdata: freeWheelUrl })
        .addTrackingScript({
          _attributes: { event: 'acceptInvitation' },
          _cdata: freeWheelUrl
        })
        .addTrackingScript({ _attributes: { event: 'close' }, _cdata: freeWheelUrl });

      return;
    }

    trackingScripts.forEach((trackingScript) => builder.addTrackingScript(trackingScript));
  }

  private setTrackingEvents(builder: VmapBuilder, trackingEvents?: TrackingType[]) {
    if (!trackingEvents?.length) {
      builder
        .addTrackingEvent({ _attributes: { event: 'breakStart' }, _cdata: freeWheelUrl })
        .addTrackingEvent({ _attributes: { event: 'breakEnd' }, _cdata: freeWheelUrl });

      return;
    }

    trackingEvents.forEach((trackingEvent) => builder.addTrackingEvent(trackingEvent));
  }

  private setImpressions(builder: VmapBuilder, impressions?: AdImpression[]) {
    if (!impressions?.length) {
      builder.addImpression({
        _attributes: { id: faker.string.uuid() },
        _cdata: freeWheelUrl
      });

      return;
    }

    impressions.forEach((impression) => builder.addImpression(impression));
  }
}
