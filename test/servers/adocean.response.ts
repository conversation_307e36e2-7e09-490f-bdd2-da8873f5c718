/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import { xmlParser, Vast4Normalized } from 'adpod-tools';
import { E2eTestModule } from '../common/e2e-test.module';
import { http, HttpResponse, RequestHandlerOptions } from 'msw';
import { HttpStatus } from '@nestjs/common';
import { faker } from '@faker-js/faker';

export const adOceanUrl = 'http://tvn.adocean.pl/ad.xml';

export type AdOceanVast = {
  Duration?: { _text: string };
  AdTitle?: { _text: string };
  AdSystem?: { _text: string; _attributes?: { version: string } };
  MediaFiles?: Array<{
    _attributes: {
      id: string;
      delivery: string;
      width: number;
      height: number;
      type: string;
      bitrate: number;
      scalable: string;
      maintainAspectRatio: string;
    };
    _cdata: string;
  }>;
  TrackingEvents?: Array<{
    _attributes: { event: string };
    _cdata: string;
  }>;
  Impressions?: Array<{
    _attributes: { id: string };
    _cdata: string;
  }>;
};

export type AdOceanContext = {
  adOceanResponse: Vast4Normalized;
  adOceanRequestUrls: string[];
};

export class AdOceanResponse extends E2eTestModule<AdOceanContext> {
  mockAdOceanResponse(vast?: AdOceanVast, options?: RequestHandlerOptions) {
    this.environment.httpMockServer.use(
      http.get(
        adOceanUrl,
        ({ request }) => {
          try {
            const response = this.prepareResponse(vast);

            this.context.merge({
              adOceanResponse: response,
              adOceanRequestUrls: [
                ...(this.context.get().adOceanRequestUrls ?? []),
                request.url
              ]
            });

            return HttpResponse.xml(xmlParser.fromJSONtoXML(response));
          } catch (error) {
            return HttpResponse.json({ error }, { status: HttpStatus.INTERNAL_SERVER_ERROR });
          }
        },
        options
      )
    );
  }

  mockAdOceanEmptyResponse(options?: RequestHandlerOptions) {
    this.environment.httpMockServer.use(
      http.get(
        adOceanUrl,
        ({ request }) => {
          this.context.merge({
            adOceanRequestUrls: [...(this.context.get().adOceanRequestUrls ?? []), request.url]
          });

          return HttpResponse.xml('<VAST version="4.0"></VAST>');
        },
        options
      )
    );
  }

  private prepareResponse(vast?: AdOceanVast): Vast4Normalized {
    const adId = faker.string.uuid();
    const creativeId = faker.string.uuid();
    const breakId = faker.string.numeric({ length: 16 });

    return {
      VAST: {
        _attributes: {
          version: '4.0',
          'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
          xmlns: 'http://www.iab.com/VAST'
        },
        Ad: [
          {
            _attributes: {
              id: adId,
              sequence: 1,
              breakId: breakId,
              conditionalAd: false
            },
            InLine: {
              AdSystem: vast?.AdSystem || {
                _text: 'AdOcean',
                _attributes: { version: '4.0' }
              },
              AdTitle: vast?.AdTitle || {
                _text: '[Test] AdOcean Test Ad 15s'
              },
              Error: [
                {
                  _attributes: { id: '1' },
                  _cdata: `${adOceanUrl}?error=[ERRORCODE]`
                }
              ],
              Impression: vast?.Impressions || [
                {
                  _attributes: { id: '1' },
                  _cdata: `${adOceanUrl}?event=impression&id=${adId}`
                }
              ],
              Creatives: {
                Creative: [
                  {
                    _attributes: {
                      id: creativeId,
                      AdID: adId
                    },
                    UniversalAdId: {
                      _attributes: {
                        idRegistry: 'adocean',
                        idValue: adId
                      },
                      _text: adId
                    },
                    Linear: {
                      Duration: vast?.Duration || { _text: '00:00:15' },
                      TrackingEvents: {
                        Tracking: vast?.TrackingEvents || this.getDefaultTrackingEvents(adId)
                      },
                      VideoClicks: {
                        ClickThrough: {
                          _attributes: { id: 'AdOcean' },
                          _cdata: `${adOceanUrl}?event=click&id=${adId}`
                        }
                      },
                      MediaFiles: {
                        MediaFile: vast?.MediaFiles || this.getDefaultMediaFiles()
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    };
  }

  private getDefaultTrackingEvents(adId: string) {
    const events = [
      'start',
      'firstQuartile',
      'midpoint',
      'thirdQuartile',
      'complete',
      'mute',
      'unmute',
      'pause',
      'resume',
      'creativeView'
    ];

    return events.map((event) => ({
      _attributes: { event },
      _cdata: `${adOceanUrl}?event=${event}&id=${adId}`
    }));
  }

  private getDefaultMediaFiles() {
    return [
      {
        _attributes: {
          id: 'AdOcean',
          delivery: 'progressive',
          width: 1280,
          height: 720,
          type: 'video/mp4',
          bitrate: 2000,
          scalable: 'true',
          maintainAspectRatio: 'true'
        },
        _cdata: `${adOceanUrl}/media/test-ad-1280x720.mp4`
      },
      {
        _attributes: {
          id: 'AdOcean',
          delivery: 'progressive',
          width: 640,
          height: 360,
          type: 'video/mp4',
          bitrate: 1000,
          scalable: 'true',
          maintainAspectRatio: 'true'
        },
        _cdata: `${adOceanUrl}/media/test-ad-640x360.mp4`
      }
    ];
  }
}
