## Prerequisites

Before you can run the e2e tests, please ensure you have the following installed and running on your local machine:

- **Docker Desktop**

## Setup

The e2e tests require a specific set of services to be running. These are managed via docker compose.

### Environment Configuration

The application and tests may need environment variables to connect to the Docker services. These are usually managed in a `.env.test` file. Make sure your `.env.test` file is correctly configured for the testing environment.

## Running E2E Tests

The e2e tests are executed using Jest. The configuration is defined in `jest/jest.e2e.config.js`, which looks for test files in the `test/` directory with a `.e2e-spec.ts` extension.

### Using npm script

You can run all E2E tests with:

```bash
npm run test:e2e
```

The tests are configured to run serially (`maxWorkers: 1`) to prevent conflicts with shared resources.

For future improvement the setup can be adjusted to work with multiple workers to speed up tests execution.
