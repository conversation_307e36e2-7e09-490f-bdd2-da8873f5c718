import { Channel, IConfiguration } from 'adpod-tools';
import { E2eTestModule } from '../common';
import { validators } from '../../src/EnvValidation/envalidConfig';
import dayjs from 'dayjs';
import { ConfigurationContext } from '../builders';
import assert from 'assert';
import { UpdateService } from '../../src/updater/update.service';

export class ConfigurationRequest extends E2eTestModule<ConfigurationContext> {
  async uploadConfiguration(date = dayjs()) {
    const { configuration } = this.context.get();
    assert(configuration, 'conifuration is not defined in the context');

    await this.upload(configuration, date);

    await this.refreshConfiguration();
  }

  async uploadConfigurations(date = dayjs()) {
    const { configurations } = this.context.get();
    assert(configurations, 'configurations is not defined in the context');

    await Promise.all(configurations.map((configuration) => this.upload(configuration, date)));

    await this.refreshConfiguration();
  }

  private async refreshConfiguration() {
    await this.environment.app.get(UpdateService).update();
  }

  private async upload(configuration: IConfiguration, date: dayjs.Dayjs) {
    const { channel, version } = configuration;
    await this.environment.s3.putObject({
      Bucket: validators.S3_BUCKET_NAME,
      Key: this.getKey(date, channel, version),
      Body: JSON.stringify(configuration),
      ContentType: 'application/json'
    });
  }

  private getKey(date: dayjs.Dayjs, channel: Channel, version: string) {
    return `${date.format('YYYYMMDD')}/${channel}__${version}__${dayjs().toISOString()}.json`;
  }
}
