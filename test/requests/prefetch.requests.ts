import assert from 'assert';
import { PrefetchPlaylistQuery } from '../../src/components/playlist-multiple/validate/prefetchPlaylistQuery';
import { ConfigurationContext } from '../builders';
import { E2eTestModule, joinQueryParams } from '../common';
import { xmlParser } from 'adpod-tools';
import { VmapNormalized } from '../../src/interfaces';
import { VmapLookup } from '../common/helpers/vmap-lookup';

export type PrefetchPlaylistContext = ConfigurationContext & {
  prefetchPlaylist: VmapNormalized;
  prefetchPlaylistLookup: VmapLookup;
};

export class PrefetchPlaylistRequest extends E2eTestModule<PrefetchPlaylistContext> {
  async prefetchPlaylist(
    query?: PrefetchPlaylistQuery
  ): Promise<Pick<PrefetchPlaylistContext, 'prefetchPlaylist' | 'prefetchPlaylistLookup'>> {
    const { configuration } = this.context.get();
    assert(configuration, 'configuration is not provided in the context');

    const joinedQuery: Partial<PrefetchPlaylistQuery> = {
      ch: configuration.channel,
      v: configuration.version,
      ...query
    };

    const response = await this.get<string>(
      `api/playlist/prefetch${joinQueryParams(joinedQuery)}`
    );

    const prefetchPlaylist = xmlParser.fromXMLtoJSON(response.data) as VmapNormalized;
    return this.context.merge({
      prefetchPlaylist,
      prefetchPlaylistLookup: new VmapLookup(prefetchPlaylist)
    });
  }
}
