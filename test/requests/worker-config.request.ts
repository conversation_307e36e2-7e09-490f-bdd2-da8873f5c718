import { E2eTestModule } from '../common';
import { validators } from '../../src/EnvValidation/envalidConfig';
import { WorkerConfigType } from '../../src/models/workerConfig';
import { workerConfigSeed } from '../seeds/worker-config';
import _ from 'lodash';

export type WorkerConfigContext = {
  workerConfig: WorkerConfigType;
};

export class WorkerConfigRequest extends E2eTestModule<WorkerConfigContext> {
  async uploadWorkerConfig(workerConfig?: Partial<WorkerConfigType>) {
    await this.uploadToBucket(workerConfig);

    await this.get<void>(`api/config/updateWorkerConfig`);
  }

  private async uploadToBucket(workerConfig?: Partial<WorkerConfigType>) {
    const mergedWorkerConfig = this.prepareWorkerConfig(workerConfig);

    await this.environment.s3.putObject({
      Bucket: validators.S3_BUCKET_NAME,
      Key: validators.S3_WORKER_CONFIG_PATH,
      Body: JSON.stringify(mergedWorkerConfig),
      ContentType: 'application/json'
    });
  }

  private prepareWorkerConfig(data?: Partial<WorkerConfigType>) {
    return _.merge(workerConfigSeed, data);
  }
}
