/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import {
  AdError,
  AdImpression,
  AdTitle,
  AdTracking,
  AdVast4Normalized,
  Extension,
  MediaFile,
  RegistryId,
  TextType,
  Vast4Normalized
} from 'adpod-tools';
import _ from 'lodash';
import { faker } from '@faker-js/faker';
import { DeepPartial } from '../common/types';

export class VastBuilder {
  public vast: Vast4Normalized;

  public lastAdslot: AdVast4Normalized;

  addVast(data?: DeepPartial<Vast4Normalized>) {
    const vast: Vast4Normalized = {
      VAST: {
        Ad: [],
        _attributes: {
          version: '4.0',
          'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
          xmlns: 'http://www.iab.com/VAST'
        }
      }
    };

    this.vast = _.merge(vast, data);

    return this;
  }

  addVastAdSlot(data?: DeepPartial<AdVast4Normalized>) {
    const spotId = `r${faker.string.numeric({ length: 6 })}.mov`;
    const position = this.vast.VAST.Ad.length + 1;

    const vastAdSlot: AdVast4Normalized = {
      _attributes: {
        id: spotId,
        breakId: faker.string.numeric({ length: 16 }),
        sequence: position
      },
      InLine: {
        AdSystem: { _text: faker.company.name() },
        AdTitle: { _text: faker.helpers.arrayElement(Object.values(AdTitle)) },
        Creatives: {
          Creative: [
            {
              _attributes: { id: spotId },
              UniversalAdId: {
                _attributes: {
                  idRegistry: faker.helpers.arrayElement(Object.values(RegistryId)),
                  idValue: spotId
                },
                _text: spotId
              },
              Linear: {
                Duration: { _text: `00:00:${faker.number.int({ min: 10, max: 45 })}` },
                MediaFiles: {
                  MediaFile: []
                },
                TrackingEvents: {
                  Tracking: []
                }
              }
            }
          ]
        },
        Impression: [],
        Error: []
      }
    };

    const mergedVastAdSlot = _.merge(vastAdSlot, data);
    this.vast.VAST.Ad.push(mergedVastAdSlot);
    this.lastAdslot = mergedVastAdSlot;

    return this;
  }

  addTrackingScript(data?: AdTracking) {
    data &&
      this.lastAdslot.InLine?.Creatives.Creative[0].Linear?.TrackingEvents.Tracking.push(data);
    return this;
  }

  addMediaFile(data?: MediaFile) {
    const mediaFile: MediaFile = {
      _attributes: {
        width: 230,
        height: 400,
        type: 'application/mxf',
        delivery: 'progressive'
      },
      _cdata: `${faker.internet.url()}/${faker.string.alphanumeric()}.${faker.helpers.arrayElement(['mov', 'mxf', 'mp4'])}`
    };

    const mergedMediaFile = _.merge(mediaFile, data);
    this.lastAdslot.InLine?.Creatives.Creative[0].Linear?.MediaFiles.MediaFile.push(
      mergedMediaFile
    );

    return this;
  }

  addDuration(data?: DeepPartial<TextType>) {
    _.merge(this.lastAdslot.InLine?.Creatives.Creative[0].Linear?.Duration, data);
    return this;
  }

  addImpression(data?: AdImpression) {
    data && this.lastAdslot.InLine?.Impression.push(data);
    return this;
  }

  addError(data?: AdError) {
    data && this.lastAdslot.InLine?.Error.push(data);
    return this;
  }

  addExtension(data?: Extension) {
    data && this.lastAdslot.InLine?.Extensions?.Extension.push(data);
    return this;
  }

  build() {
    return this.vast;
  }
}
