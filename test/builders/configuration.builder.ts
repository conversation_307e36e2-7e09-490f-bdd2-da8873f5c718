import {
  AdTitle,
  AdType,
  AdVast4Normalized,
  BreakConnector,
  Channel,
  IAd,
  IConfiguration,
  OrderType,
  ParamsMacro,
  RegistryId,
  TMirrored
} from 'adpod-tools';
import dayjs from 'dayjs';
import _ from 'lodash';
import { faker } from '@faker-js/faker';
import { E2eTestModule } from '../common/e2e-test.module';
import { DeepPartial } from '../common/types';

export type ConfigurationContext = {
  configuration: IConfiguration;
  configurations: IConfiguration[];
};

export class ConfigurationBuilder extends E2eTestModule<ConfigurationContext> {
  private configuration: IConfiguration;

  private lastAdslot: IAd;

  addConfiguration(data?: DeepPartial<IConfiguration>) {
    const configuration: IConfiguration = {
      id: faker.string.uuid(),
      channel: faker.helpers.arrayElement(Object.values(Channel)),
      version: 'v1_0_0',
      time: dayjs().format('YYYY-MM-DDTHH:mm:ssZ'),
      duration: faker.number.int({ min: 10, max: 45 }),
      adslot: [],
      metadata: {
        programme: faker.person.firstName(),
        externalAdBreakID: faker.string.alphanumeric(),
        deapProfiles: faker.datatype.boolean(),
        trackingDaiAds: faker.datatype.boolean(),
        exactLenght: faker.datatype.boolean(),
        diffCreatives: faker.datatype.boolean(),
        additionalTrackingsFW: faker.datatype.boolean(),
        preroll: faker.datatype.boolean(),
        hasAtvSlot: faker.datatype.boolean()
      }
    };

    this.configuration = _.merge(configuration, data);
  }

  addAdSlot(data?: DeepPartial<IAd>) {
    const adslot: IAd = {
      position: this.configuration.adslot.length + 1,
      duration: faker.number.int({ min: 10, max: 45 }),
      type: faker.helpers.arrayElement(Object.values(AdType)),
      adrequest: '',
      connector:
        this.configuration.breakConnector ??
        faker.helpers.arrayElement(Object.values(BreakConnector)),
      metadata: {
        breakId: faker.string.uuid(),
        orderId: faker.string.uuid(),
        adId: `r${faker.string.numeric({ length: 6 })}.mov`,
        agencyId: faker.string.numeric({ length: 8 }),
        advertiserId: faker.string.numeric({ length: 4 }),
        orderType: faker.helpers.arrayElement(Object.values(OrderType)),
        atvType: {
          pending: [],
          processed: []
        }
      },
      vastmirroredadsJson: {
        VAST: {
          Ad: [],
          _attributes: {
            version: '4.0',
            'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
            xmlns: 'http://www.iab.com/VAST'
          }
        }
      }
    };

    const mergedAdslot = _.merge(adslot, data);
    this.configuration.adslot.push(mergedAdslot);
    this.lastAdslot = mergedAdslot;
  }

  addAdSlotVast(data?: DeepPartial<AdVast4Normalized>) {
    const spotId =
      this.lastAdslot.metadata.adId ?? `r${faker.string.numeric({ length: 6 })}.mov`;
    const mezzanine = `http://mezzanie-url.pl/file/${spotId}`;
    const { duration, position } = this.lastAdslot;
    const { version, channel } = this.configuration;
    const bid = faker.string.numeric({ length: 16 });
    const adTitle = faker.helpers.arrayElement(Object.values(AdTitle));
    const registryId = faker.helpers.arrayElement(Object.values(RegistryId));
    const tMirrored = faker.helpers.arrayElement(Object.values(TMirrored));
    const mediaType = 'video/quicktime';
    const percent = faker.number.int({ min: 0, max: 20 });

    const url = this.url({
      spotId,
      mezzanine,
      duration,
      position,
      version,
      bid,
      adTitle,
      channel,
      registryId,
      tMirrored,
      mediaType,
      percent
    });

    const adslot: AdVast4Normalized = {
      _attributes: {
        id: spotId,
        breakId: bid,
        sequence: position,
        conditionalAd: false,
        linear: 'true'
      },
      InLine: {
        AdSystem: { _text: 'SYSTEM' },
        AdTitle: { _text: adTitle },
        Creatives: {
          Creative: [
            {
              _attributes: { id: spotId },
              UniversalAdId: {
                _attributes: { idRegistry: registryId, idValue: spotId },
                _text: spotId
              },
              Linear: {
                Duration: { _text: `00:00:${duration}` },
                MediaFiles: {
                  MediaFile: [
                    {
                      _attributes: {
                        width: faker.number.int({ min: 400, max: 1920 }),
                        type: 'video/mp4',
                        height: faker.number.int({ min: 200, max: 1080 }),
                        delivery: 'progressive'
                      },
                      _cdata: mezzanine
                    }
                  ],
                  Mezzanine: {
                    _cdata: mezzanine,
                    _attributes: {
                      delivery: 'progressive',
                      type: mediaType,
                      width: faker.number.int({ min: 400, max: 1920 }),
                      height: faker.number.int({ min: 200, max: 1080 })
                    }
                  }
                },
                TrackingEvents: {
                  Tracking: [
                    { _attributes: { event: 'start' }, _cdata: url },
                    { _attributes: { event: 'firstQuartile' }, _cdata: url },
                    { _attributes: { event: 'midpoint' }, _cdata: url },
                    { _attributes: { event: 'thirdQuartile' }, _cdata: url },
                    { _attributes: { event: 'complete' }, _cdata: url }
                  ]
                }
              }
            }
          ]
        },
        Impression: [{ _cdata: url }],
        Error: [{ _cdata: url }]
      }
    };

    const mergedAdSlot = _.merge(adslot, data);
    this.lastAdslot.vastmirroredadsJson?.VAST.Ad.push(mergedAdSlot);
  }

  build() {
    return this.context.merge({
      configuration: this.configuration,
      configurations: [...(this.context.get().configurations ?? []), this.configuration]
    });
  }

  private url({
    position,
    bid,
    channel,
    spotId,
    version,
    tMirrored,
    duration,
    percent
  }: ImpressionData) {
    return `http://mirrored-url.pl/?ed=p%3D${position}%2Fbid%3D${bid}%2Fch%3D${channel}%2Fuadid%3D${spotId}%2Fv%3D${version}%2Ft%3D${tMirrored}%2Fuid%3D${ParamsMacro.uid}%2Fdur%3D${duration}%2Fe%3D${percent}%2Fbt%3D${ParamsMacro.breakType}%2Fcust_params%3D${ParamsMacro.custParams}%2Fapp_version%3D${ParamsMacro.appVersion}`;
  }
}

type ImpressionData = {
  spotId: string;
  mezzanine: string;
  duration: number;
  position: number;
  version: string;
  bid: string;
  adTitle: AdTitle;
  channel: Channel;
  registryId: RegistryId;
  tMirrored: TMirrored;
  mediaType: string;
  percent: number;
};
