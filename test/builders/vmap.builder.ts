/* eslint-disable no-unused-expressions */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import {
  AdError,
  AdImpression,
  AdTracking,
  AdVast4Normalized,
  Extension,
  MediaFile,
  TextType
} from 'adpod-tools';
import _ from 'lodash';
import { faker } from '@faker-js/faker';
import { DeepPartial } from '../common/types';
import { TrackingType, VmapNormalized } from '../../src/interfaces';
import { VastBuilder } from './vast.builder';

export class VmapBuilder {
  private vmap: VmapNormalized;

  constructor(private readonly vastBuilder: VastBuilder) {}

  addVmap(data?: DeepPartial<VmapNormalized>) {
    const vmap: VmapNormalized = {
      'vmap:VMAP': {
        _attributes: {
          version: '1.0',
          'xmlns:vmap': 'http://www.iab.net/vmap-1.0'
        },
        'vmap:AdBreak': [
          {
            _attributes: {
              timeOffset: 'start',
              breakType: 'linear',
              breakId: faker.string.numeric({ length: 9 })
            },
            'vmap:AdSource': {
              _attributes: {
                id: '1',
                allowMultipleAds: 'true',
                followRedirects: 'true'
              },
              'vmap:VASTAdData': this.vastBuilder.addVast().build()
            }
          }
        ]
      }
    };

    this.vmap = _.merge(vmap, data);

    return this;
  }

  addVastAdSlot(data?: DeepPartial<AdVast4Normalized>) {
    const breakId =
      data?._attributes?.breakId ??
      this.vmap['vmap:VMAP']['vmap:AdBreak'][0]._attributes.breakId;

    this.vmap['vmap:VMAP']['vmap:AdBreak'][0]['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad.push(
      this.vastBuilder.addVastAdSlot({
        ...data,
        _attributes: { ...data?._attributes, breakId }
      }).lastAdslot
    );

    return this;
  }

  addTrackingScript(data?: AdTracking) {
    this.vastBuilder.addTrackingScript(data);
    return this;
  }

  addMediaFile(data?: MediaFile) {
    this.vastBuilder.addMediaFile(data);
    return this;
  }

  addDuration(data?: DeepPartial<TextType>) {
    this.vastBuilder.addDuration(data);
    return this;
  }

  addImpression(data?: AdImpression) {
    this.vastBuilder.addImpression(data);
    return this;
  }

  addError(data?: AdError) {
    this.vastBuilder.addError(data);
    return this;
  }

  addExtension(data?: Extension) {
    this.vastBuilder.addExtension(data);
    return this;
  }

  addTrackingEvent(data?: TrackingType) {
    data &&
      this.vmap['vmap:VMAP']['vmap:AdBreak'][0]['vmap:TrackingEvents']?.['vmap:Tracking'].push(
        data
      );

    return this;
  }

  build() {
    return this.vmap;
  }
}
