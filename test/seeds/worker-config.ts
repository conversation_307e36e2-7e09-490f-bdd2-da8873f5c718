import { WorkerConfigType } from '../../src/models/workerConfig';

export const workerConfigSeed: WorkerConfigType = {
  loggingGroups: {
    error: 'on',
    warn: 'on',
    info: 'on',
    debug: 'on',
    external: 'on',
    cache: 'on',
    dev: 'on',
    snowflake: 'on',
    whatson: 'on',
    configsCache: 'on',
    statsPrefetch: 'on',
    statsGet: 'on',
    statsBreak: 'on',
    statsAdserverAdo: 'on',
    statsAdserverAdoBreak: 'on',
    statsAdserverAdoBreakExactLength: 'on',
    statsAdserverAdoBreakSpecyfic: 'on',
    statsAdserverFw: 'on',
    statsAdserverGam: 'on',
    startOver: 'on',
    adoSlotDebug: 'on',
    adoBreakDebug: 'on'
  },
  versionLogging: [],
  playlistSingleTimeThreshold: 60,
  programmaticEnchancedVastPlversions: [],
  snowflake: {
    ipLogging: true,
    snowFlakeEnabledConfigs: []
  },
  scheduleConfigsAvailability: {
    last: 1,
    next: 3,
    keepOutOfRangeConfigs: true,
    outOfRangeConfigsTTL: 4
  },
  freeWheelDuration: {}
};
