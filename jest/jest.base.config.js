module.exports = {
  moduleFileExtensions: ['ts', 'js', 'json'],
  rootDir: '../',
  testEnvironment: 'node',
  runtime: '@side/jest-runtime',
  transform: {
    '^.+\\.(t|j)s$': '@swc/jest'
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coverageDirectory: './coverage',
  coverageReporters: ['text', 'cobertura', 'lcov'],
  testPathIgnorePatterns: ['<rootDir>/src/assets/scenarios', '<rootDir>/src/assets/logs'],
  reporters: [
    'default',
    [
      'jest-junit',
      {
        suiteName: 'jest tests',
        outputDirectory: 'output/coverage',
        outputName: 'junit.xml',
        classNameTemplate: '{classname} - {title}',
        titleTemplate: '{classname} - {title}',
        ancestorSeparator: ' > ',
        usePathForSuiteName: 'true'
      }
    ]
  ]
};
