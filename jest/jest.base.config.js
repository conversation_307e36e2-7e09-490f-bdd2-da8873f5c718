module.exports = {
  moduleFileExtensions: ['ts', 'js', 'json'],
  rootDir: '../',
  testEnvironment: 'node',
  runtime: '@side/jest-runtime',
  transform: {
    '^.+\\.(t|j)s$': '@swc/jest'
  },
  collectCoverageFrom: ['**/*.(t|j)s'],
  coveragePathIgnorePatterns: [
    'src/interceptors',
    'src/middleware',
    'src/interfaces',
    'src/models',
    'src/main.ts',
    'src/app.module.ts',
    'src/assets/configs/appLogs.config.ts',
    'src/initSwagger.ts',
    'src/components/info/info.controller.ts',
    '.doc.decorator.ts'
  ],
  coverageDirectory: './coverage',
  coverageReporters: ['text', 'cobertura', 'lcov'],
  testPathIgnorePatterns: ['<rootDir>/src/assets/scenarios', '<rootDir>/src/assets/logs'],
  reporters: [
    'default',
    [
      'jest-junit',
      {
        suiteName: 'jest tests',
        outputDirectory: 'output/coverage',
        outputName: 'junit.xml',
        classNameTemplate: '{classname} - {title}',
        titleTemplate: '{classname} - {title}',
        ancestorSeparator: ' > ',
        usePathForSuiteName: 'true'
      }
    ]
  ]
};
