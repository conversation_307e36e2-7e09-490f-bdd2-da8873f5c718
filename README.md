# Ad decisioning/AdPod Worker

AdPod Worker is a modern web service crafted to provide advertising abstraction layer which can specify television ads for each customer.

## Project set up

At first, need to dowloand this repo on your personal computer, later just run command npm install to get all required packages, and type _npm run start:dev_. You should see massage in console about app listening on specific port. So, go to localhost.
Development process requires writing unit tests (to keep project available). Type in termal `npm run test`

## Commands

- Get packages: `npm install`
- Run application in production mode: `npm start`
- Run application in development mode: `npm run start:dev`
- Watch all test suites: `npm run test`

### Pull Requests

This document outlines the automated testing and merge workflow for GitHub pull requests. The goal is to ensure that all pull requests undergo thorough testing and approval before being merged into the main branch.

- **Automated Testing**: Automated tests must run successfully for each pull request. This ensures that the code changes do not introduce any regressions or errors.
- **Approval**: At least one approval is required from a designated reviewer, as configured in the GitHub settings.
- **Merge**: Once the automated tests have passed and the required approvals have been obtained, the pull request can be merged into the main branch.
- **Skip CI checks**: If you need to bypass the automated testing for a specific pull request, you can add a commit message that contains **[ci skip]** to the pull request

## Update dependencies

The command to update the dependencies in the project is `bash scripts/update-dependencies.sh` executed in the root directory of the project. After executing the command, the UI will appear in which you can choose what dependencies you want to update.

### The process of updating dependencies

First, select all patch level dependencies and perform the update. In theory, `patch` level dependencies only contain bug fixes, so the risk of breaking changes is quite low and you can update all dependencies at once. After updating the dependencies, run `npm i` to make sure everything is working properly.

Then rerun the dependency update command and update all `minor` level dependencies. In theory, minor level dependencies should only contain new functionality, but in practice there is a good chance that something will not work (e.g. type definitions have changed, some library APIs have changed). However, these changes should be so few that updating all such dependencies at once should not be a big problem. After updating the dependencies, run `npm i` to make sure everything is working properly.

Now we need to update the dependencies of type `major`. `major` level dependencies should usually be updated one by one, as there is a good chance that they will contain breaking changes that will not be easy to fix. Run `npm i` after each dependency update.

### What to do if a library cannot be updated to the latest version

Sometimes, a library cannot be updated to the latest version because it does not work or uses esmodules that are not supported by our current node version. In such a situation, you should look for what may be the cause of the error (e.g. on GitHub or StackOverflow) and try to solve it. If the problem does not seem to have a solution that works you should try to update the package to the latest working version.

### Disable checks for updates

Sometimes there may be a situation where we don't want to update certain libraries and we want them not to show up in the update UI. For example, the `eslint` library cannot be updated to the newer version until we organize the external rule sets which uses older version of eslint. In this case, add the selected library to the list of ignored libraries in the script `scripts/update-dependencies.sh`.

### Tips

- All @nestjs/\* packages should be updated at the same time, no matter what level of dependency they have.

## Documentation

The following documentationa are available for this project::

- [AdPod Worker/Ad-decisioning](https://confluence.pl.grupa.iti/display/ADTECH/AdPod+Worker)
- [AdPod Ecosystem](https://confluence.pl.grupa.iti/display/ADTECH/AdPod+Ekosystem)

## Changelog

### Version 2.129.2 (26 August 2025)

ATV-1038 update GAM response add attributes to MediaFile and Mezzanine tags
ATV-1036 remove version="4.0" attr from replaced slots

### Version 2.129.1 (26 August 2025)

ATV-1028 add Debug tag to replaced ads slots

### Version 2.129.0 (21 August 2025)

ATV-965 - Poprawa wpisów w logowaniu req/resp (creative/ip)
ATV-988 - Obsługa cust_params
ATV-924 - Refactor - dodanie serwisów dedykowanych do modyfikacji VASTa

### Version 2.128.11 (20 August 2025)

ATV-1017 Stabilizacja workera pod katem R115

### Version 2.128.10 (20 August 2025)

ATV-1017 Stabilizacja workera pod katem R115

### Version 2.128.9 (14 August 2025)

ATV-1017 Stabilizacja workera pod katem R115

### Version 2.128.8 (12 August 2025)

ATV-1017 Stabilizacja workera pod katem R115

### Version 2.128.7 (07 August 2025)

Revert ATV-829 delete gdpr_consent whenever gdpr=0

### Version 2.128.6 (06 August 2025)

ATV-1014 - breakType dla next_mirrored jest równy 'dai'

### Version 2.128.5 (30 July 2025)

ATV-1000 - Programmatic - problem ze zliczeniami GAM/DV360

### Version 2.128.4 (28 July 2025)

ATV-1006 remove toLowerCase form enhanced VAST url
ATV-829 delete gdpr_consent whenever gdpr=0

### Version 2.128.3 (23 July 2025)

ATV-1000 - Programmatic - problem ze zliczeniami GAM/DV360

### Version 2.128.1 (21 July 2025)

ATV-996 - Błędne środowisko w info/app

### Version 2.128.0 (16 July 2025)

ATV-980 - Błędne tracking skrypty dla GET
ATV-922 - Opakowanie redisa i aws s3 w moduly
ATV-831 - Uruchamianie logowania dla wybranego configu

### Version 2.127.4 (16 July 2025)

ATV-847 - Optymalizacja zużycia pamięci poprzez redukcję danych w app cache

### Version 2.127.3 (9 July 2025)

ATV-847 - Optymalizacja zużycia pamięci poprzez redukcję danych w app cache

### Version 2.127.0 (28 June 2025)

ATV-863 - Dodanie języka dla GAM
ATV-829 - GDPR w AdPod Manager
ATV-847 - Optymalizacja zużycia pamięci poprzez redukcję danych w app cache
ATV-865 - Usunięcie funkcjonalności prefetchCap
ATV-866 - Usunięcie endpoint dla "splash screen"
ATV-868 - Usunięcie z URL FW parametru "test=mxf"
ATV-957 - Błędna obsługa reklam z FW bez tagi Creative

### Version 2.126.12 (24 July 2025)

ATV-1006 remove toLowerCase form enhanced VAST url

### Version 2.126.11 (08 July 2025)

ATV-982 add mezzanine attrs
ATV-981 skip enhanced vast request without UniversalAdId

### Version 2.126.10 (01 July 2025)

ATV-979 HTTP 500 dla serwisu obsługującego FreeWheel

### Version 2.126.9 (30 June 2025)

ATV-974 Blokada dodania parametru green do części endpointów

### Version 2.126.8 (26 June 2025)

merge with release 113.5
ATV-928 change app cache lib, use async for app cache operations

### Version 2.126.6 (06 June 2025)

merge with release 113.4

### Version 2.126.5 (28 May 2025)

ATV-928 - Problem z wyciekami pamieci w adpod 114

### Version 2.126.4 (22 May 2025)

ATV-832 - Update node i zależności

### Version 2.126.1 (14 May 2025)

ATV-832 - Update node i zależności

### Version 2.126.0 (8 May 2025)

ATV-832 - Update node i zależności
ATV-830 - Usunięcie profilera z workera
ATV-896 - Worker - refactor cache module

### Version 2.125.9 (25 June 2025)

ATV-973 trim mezzanine cData

### Version 2.125.8 (25 June 2025)

ATV-973 handle mixed direct & programmatic FW responses

### Version 2.125.7 (23 June 2025)

ATV-969 - trim cData before MediaFile & Mezzanine handling, support all dai ads, improve logging

### Version 2.125.6 (17 June 2025)

ATV-969 Zmiana w Media Files dla FreeWheel programmatic

### Version 2.125.5 (30 May 2025)

BUGADPOD-31 fix multiple FW ads Impression script params

### Version 2.125.4 (19 May 2025)

ATV-921 Dodanie UniversalAdId do SnowFlake response log

### Version 2.125.3 (5 May 2025)

ATV-870 MediaFile type attt base on mezzanine URL extension

### Version 2.125.2 (10 April 2025)

ATV-836 - Dodanie informacji o wersji aplikacji do dodatkowych tracking skryptów

### Version 2.125.1 (4 April 2025)

ATV-836 - Dodanie informacji o wersji aplikacji do dodatkowych tracking skryptów

### Version 2.125.0 (4 April 2025)

ATV-837 - Zmiany z sterowaniu logowaniem SnowFlake
ATV-843 - Zmiany w integracji z Centrum Restrykcji
ATV-872 - [AdPod] Problem z parsowaniem odpowiedzi dla trybu debug GAM/FWD
ATV-836 - Dodanie informacji o wersji aplikacji do logu SnowFlake oraz do tracking skryptów
ATV-861 - Usunięcie nadpisywania tagu Mezzanine (FreeWheel)
ATV-880 - Błędne działanie trybu debug dla 24h_debug

### Version 2.124.5 (18 March 2025)

ATV-855 - Merge with fillers fixes

### Version 2.124.4 (18 March 2025)

ATV-731 - Niepoprawna obsługa trybu DEBUG

### Version 2.124.3 (11 March 2025)

ATV-731 - Niepoprawna obsługa trybu DEBUG

### Version 2.124.2 (10 March 2025)

ATV-731 - Niepoprawna obsługa trybu DEBUG

### Version 2.124.0 (07 March 2025)

ATV-759 - Nie przechodzą testy na adpod-tools
ATV-643 - Usunięcie nieużywanych transformat
ATV-731 - Niepoprawna obsługa trybu DEBUG
ATV-806 - Dodanie offsetu do interwału aktualizacji app cache
ATV-812 - Aktualizacja i dodanie testów dla serwisu createJsonPlaylist
ATV-646 - Brak UID w AdRequest
ATV-803 - Brak AdRequestu dla ADO_BREAK w wywołaniach GET
ATV-807 - Zapewnienie ciągłości dostępu do danych przy przeładowaniu transformat
ATV-808 - Brak/błędny request do ADO_BREAK dla waterfall

### Version 2.123.3 (19 March 2025)

ATV-855 update adpod-aws

### Version 2.123.2 (19 March 2025)

ATV-855 fw filler bugfixing, fix full slot replacement by FW ads only, fix incorrect mezzanine adId, fix fillers tracking scripts

### Version 2.123.1 (14 March 2025)

ATV-851 add timeOffset attr to mirrored slots

### Version 2.123.0 (04 March 2025)

ATV-828 Nadmiarowe próby requestów po reklamy DAI na slotach w configu pod ADO BREAK

### Version 2.122.0 (28 February 2025)

copy of 2.121.3

### Version 2.121.3 (27 February 2025)

ATV-821 Zwracanie wszystkich reklam z FW dla trybu fillers

### Version 2.121.2 (25 February 2025)

ATV-820 Brak mezzanine, media file oraz univerdalAdId w reklamach dai uzupełnianych przez fillery

### Version 2.121.1 (25 February 2025)

ATV-819 Brak parametrów w tracking skryptach w reklamach dai uzupełnianych przez fillery
ATV-818 Brak encodowania tracking skryptów dla fillerów

### Version 2.121.0 (14 February 2025)

ATV-771 - FreeWheel fillers

### Version 2.120.4 (12 February 2025)

ATV-804 - Uzupełnianie UniversalAdid w respoonsie z GAM dla Programmatic

### Version 2.120.3 (10 February 2025)

ATV-633 - allow deap profile for gdpr=0

### Version 2.120.2 (10 February 2025)

ATV-633 - Obsługa TCF w APM (ADO_BREAK fix)

### Version 2.120.1 (5 February 2025)

ATV-742 - Usunięcie funkcjonalności cacheBreakResponse oraz cacheResponse
ATV-614 - Waterfall dla DAI + programmatic
ATV-624 - Monitoring obciążenia operacjami async
ATV-633 - Obsługa TCF w APM
ATV-779 - Usunięcie bezpośredniej integracji z SnowFlake

### Version 2.120.0 (29 January 2025)

ATV-742 - Usunięcie funkcjonalności cacheBreakResponse oraz cacheResponse
ATV-614 - Waterfall dla DAI + programmatic
ATV-624 - Monitoring obciążenia operacjami async
ATV-633 - Obsługa TCF w APM
ATV-779 - Usunięcie bezpośredniej integracji z SnowFlake

### Version 2.119.11 (11 February 2025)

ATV-804 add universalAdId to Creative

### Version 2.119.10 (28 January 2025)

ATV-741 Usunięcie tagu attribution_url z odpowiedzi GAM

### Version 2.119.9 (24 January 2025)

ATV-777 Błąd w Adrequest po ustawieniu nagłówka x-forwarded-for

### Version 2.119.8 (23 January 2025)

ATV-775 Normalizacja VASTów zwracanych z FW bez reklam

### Version 2.119.7 (23 January 2025)

ATV-772 fix provided ip to ADO request

### Version 2.119.6 (20 January 2025)

ATV-717 fix tracking scripts for GAM non programmatic relaced slots

### Version 2.119.5 (15 January 2025)

ATV-761 make enhanced vast URL selected by version

### Version 2.119.4 (13 January 2025)

ATV-760 fix Extensions merge

### Version 2.119.3 (03 January 2025)

ATV-632 Obsługa IP oraz USER AGENT fix

### Version 2.119.2 (12 December 2024)

ATV-632 Obsługa IP oraz USER AGENT fix

### Version 2.119.1 (12 December 2024)

ATV-632 Obsługa IP oraz USER AGENT
ATV-717 [PROGRAMMATIC] Integracja programmatic z GAM - aktualizacja
ATV-619 Niepop. kody HTTP na AdPod (P) - 400-tki na ADO, 403 na FW
ATV-623 Na podstawie próbki logów diagram prezentujący czasy odpowiedzi per transformata
ATV-625 Zmiany w logowaniu w adpod-aws

### Version 2.118.4 (16 December 2024)

ATV-734 change pl.media.enhanced.live to it.media.enhanced.live

### Version 2.118.3 (10 December 2024)

ATV-725 make FW Ad optional

### Version 2.118.2 (9 December 2024)

ATV-694 optimize AdOcean error catch

### Version 2.118.1 (2 December 2024)

ATV-671 Zmiany w enumie Connector
ATV-672 Dodanie nowych endpointów do get i prefetch
ATV-673 Single break playlist bazujące na duration
ATV-697 Logowanie do snowflake oraz inne metryki
ATV-654 Start-over
ATV-693 Ograniczenie ładowania configów na podstawie fragmentu fileName
ATV-694 Usprawnienia w analityce ruchu na linii worker - AdOcean
ATV-702 Refactor pobierania profili deap
ATV-712 Timeouty dla requestów wysyłanych do AdOcean

### Version 2.117.0 (21 November 2024)

ATV-600 Dostosowanie Workera do obsługi Redis Cluster (cacheBreakResponse)
ATV-599 Dostosowanie Workera do obsługi Redis Cluster (profile DEAP)

### Version 2.116.3 (19 November 2024)

ATV-685 ExactLength nie zwraca podmienionych breaków

### Version 2.116.2 (15 November 2024)

ATV-627 revert Usunięcie z eventów trackujących sekcji 'prefetch_cap' dla endpointu 'get'

### Version 2.116.1 (12 November 2024)

ATV-626 fix Dodanie atrybutów do tagu Mediafile dla mirrored ads

### Version 2.116.0 (05 November 2024)

ATV-626 Dodanie atrybutów do tagu Mediafile dla mirrored ads
ATV-627 Usunięcie z eventów trackujących sekcji 'prefetch_cap' dla endpointu 'get'
ATV-640 Dodanie githooks w workerze
ATV-647 Normalizacja danych dla response HTTP 403 z FW
ATV-651 Usunięcie pola vastmirroredads z configów
ATV-594 Macro param $DURATION_IN_SEC powinien być uzupełniany w procesorze

### Version 2.115.0 (05 November 2024)

ATV-650 update whatsOn content check

### Version 2.113.2, 2.113.3 (28 October 2024)

ATV-639 fix cust_params double encoding

### Version 2.113.0 (11 October 2024)

ATV-560 XML2JSON uproszczenie typów, normalizacja danych
ATV-570 Usunięcie obsługi DASM (Discovery Ad Server Mock)
ATV-572 Refactoring integracji z Ad Ocean (ADO, ADO_BREAK)
ATV-603 Refactor AD Ocean - ADO PROXY
ATV-604 Refactor Ad Ocean - ADO BREAK
ATV-605 Refactor Ad Ocean - ADO BREAK - exactLength
ATV-576 Refactor obsługi parametrów macro
ATV-577 Zmiana vastmirroredads z XML na JSON
ATV-578 Ominięcie procesu matchowania slotów dla reklam mirrored
ATV-594 Macro param $DURATION_IN_SEC powinien być uzupełniany w procesorze

### Version 2.112.0 (08 October 2024)

ATV-616 fix inactive cors

### Version 2.111.1 (04 October 2024)

remove REQUEST Scope

### Version 2.111.0 (03 October 2024)

ATV-608 return whatson CONTENT only if EPG metadata is avaliable

### Version 2.109.2 (02 October 2024)

ATV-612 improve logging (ADO monitoring)

### Version 2.109.1 (27 September 2024)

ATV-609 improve performance monitoring

### Version 2.109.0 (24 September 2024)

ATV-602 add fetched config to app cache
ATV-606 add timeout handling for /get endpoint

### Version 2.108.4 (12 September 2024)

ATV-596 Deap profiles fix

### Version 2.108.3 (5 September 2024)

ATV-588 Błędna wartość parametru mode w tracking skryptach

### Version 2.108.2 (3 September 2024)

ATV-587 Zmiany w logowaniu mające na celu wyjaśnienie problemów serwisu whatson

### Version 2.108.1 (30 August 2024)

ATV-571 W breaku mirrored dla ADO brakuje AdRequestu
ATV-563 Na prerollu podmiana reklam na reklamie
ATV-518 Rozróżnienie środowiska requestów
ATV-540 Integracja programmatic z GAM
ATV-548 Przeniesienie aktualnej integracji z GAM (nie programmatic) do GAMDaiAdsProvider
ATV-549 Wykrywanie responsów programmatic z GAM
ATV-550 Modyfikacja response z GAM programmatic - podstawienie UniversalAdID do atrybutu id w tagu Creative
ATV-551 Modyfikacja response z GAM programmatic - podstawienie MediaFile
ATV-552 Przekazywanie odpowiedniego ID do parametru dai_ad w tracking skryptach

### Version 2.107.0 (1 August 2024)

ATV-546 Przypisanie logowania SnowFlake do dedykowanej grupy

### Version 2.106.0 (25 July 2024)

ATV-544 Dostosowanie do ramówki NEWS TVN24

### Version 2.105.2 (30 July 2024)

ATV-544 add get time param + thresholds

### Version 2.105.2 (25 July 2024)

ATV-545 fix doc url

### Version 2.105.1 (3 July 2024)

ATV-519 integracja z programmatic

### Version 2.103.0 (27 June 2024)

ATV-437 Naprawa trybu debug dla ADO_BREAK - nie widać req. do AdOcean w Debug na poziomie breaku
ATV-476 Przeniesienie funkcjonalności ze skryptu update.ts do modułu
ATV-486 Obsługa envów przez Envalid (worker)
ATV-507 Przeróbka Cache na Nestjs/cache
ATV-511 Przeróbka Cache na Nestjs/cache - obsługa konfigracji
ATV-517 Przeróbka Cache na Nestjs/cache - dogonienie developa

### Version 2.102.3 (25 June 2024)

ATV-529 fix mirrored ads in nonAtv breaks

### Version 2.102.2 (24 June 2024)

### Version 2.102.0 (29 May 2024)

ATV-499 fix fillers for FW_FILLER multiple adds playlist
ATV-501 fix tracking scripts for FW_FILLER multiple adds playlist
ATV-495 add workerConfig
ATV-502 optimize s3 reconnect
ATV-414 fix for SF data console logging
ATV-510 local cache deap profiles

### Version 2.101.2 (29 May 2024)

ATV-512 change gdpr_consent to gdprConsent

### Version 2.101.0 (22 May 2024)

ATV-503 limit worker cors

### Version 2.100.0 (13 May 2024)

ATV-445 add BNG integration
ATV-492 update preroll response

### Version 2.99.0 (23 April 2024)

ATV-422 add SF raw logging
ATV-483 ado exact length debug
ATV-481 handle permutive profiles

### Version 2.98.2 (28 March 2024)

ATV-455 fix 500 err cannot read id of undefined

### Version 2.98.1 (28 March 2024)

ATV-455 add dai\_ prefix for diffCreatives orange

### Version 2.98.0 (27 March 2024)

ATV-455 stats logging changes

### Version 2.97.0 (04 March 2024)

ATV-407 Dodanie profilu video dla Orange
ATV-400 Zmiana "Spot" na "Midroll"
ATV-416 Dodanie nagłówka x-tvn-links-response-proto
ATV-415 Dodanie logowania protokołu i headerów (trace + logi)

### Version 2.96.5 (14 March 2024)

ATV-432 add dai_id to FW_FILLER
ATV-429 fix duration for fillers tracking scripts

### Version 2.96.4 (12 March 2024)

ATV-424 remove gdpr from discovery engage scripts

### Version 2.96.3 (06 March 2024)

ATV-420 update fillers

### Version 2.96.2 (22 February 2024)

ATV-408 remove nc from ADO links

### Version 2.96.1 (16 February 2024)

ATV-403 Add ADO_BREAK to TCF

### Version 2.96.0 (15 February 2024)

ATV-381 Dodatkowe elementy w log apm_response (do Snowflake)
ATV-379 Dodanie obsługi TCF w APM
ATV-399 Logowanie szybkości adserver response & SF

### Version 2.95.1 (13 February 2024)

ATV-396 update trackings and fillers

### Version 2.94.1 (02 February 2024)

revert tracer config

### Version 2.94.0 (31 January 2024)

ATV-393 fix 500 err for get
ATV-390 add trace to logs
ATV-383 fix hardcoded version for fillers template

### Version 2.93.0 (30 January 2024)

ATV-383 zmiana optymalizacji kampanii dla WBD IT -> 30 = 15 +15
ATV-391 wyłączenie wybranych logów z ingest

### Version 2.92.1 (28 December 2023)

ATV-382 fix Redis cache key

### Version 2.92.0 (04 December 2023)

ATV-356 DD logging

### Version 2.91.1 (27 November 2023)

ATV-332 fix whatson content field

### Version 2.91.0 (21 November 2023)

ATV-364 fix impressions scripts

### Version 2.90.0 (10 November 2023)

ATV-360 don't rely on UniversalAdId
ATV-336 add AdParameters tag to preroll response, remove mock

### Version 2.89.0 (07 November 2023)

ATV-336 add EPGMetadata to preroll

### Version 2.88.2 (09 November 2023)

ATV-357 remove unnecessary getCallerIdentity check

### Version 2.88.1 (19 October 2023)

ATV-335 fix FW UniversalAdId

### Version 2.88.0 (13 October 2023)

ATV-335 fix FW dai_ad in impressions

### Version 2.87.0 (09 October 2023)

ATV-346 fix FW dai_ad in trackings (preroll)

### Version 2.86.0 (27 September 2023)

ATV-335 fix FW dai_ad in trackings

### Version 2.85.0 (25 September 2023)

ATV-342 Błąd przy response null z adserwera

### Version 2.84.0 (15 September 2023)

ATV-337 refactoring

### Version 2.83.0 (11 September 2023)

ATV-337 preroll response changes (tracking scripts, breaktype)

### Version 2.82.0 (08 September 2023)

ATV-332 remove mock

### Version 2.81.0 (07 September 2023)

ATV-332 preroll

### Version 2.80.0 (03 August 2023)

ATV-331 add preroll mock

### Version 2.79.0 (01 August 2023)

ATV-327, Improve FW reponse verification

### Version 2.78.0 (27 July 2023)

ATV-327, Improve FW Trackings and Impressions events

### Version 2.77.0 (22 June 2023)

ATV-321, Doc update
ATV-316, Prefetch cap
ATV-314, Additional FW trackings based on WorkerConfig

### Version 2.75.1 (20 June 2023)

ATV-323, Add duration to FW request

### Version 2.75.0 (15 June 2023)

ATV-315, Update swagger ('duration' param)

### Version 2.76.0 (13 June 2023)

ATV-316, Add prefetch cap

### Version 2.75.0 (06 June 2023)

ATV-318, Weryfikacja i usprawnienie działania logów z CR na procesorze
ATV-317, Procesor powinien zwracać błąd HTTP 260 zamiast 460

### Version 2.74.0 (18 May 2023)

ATV-303, Add dataDog integration
ATV-309, Add ch to FW requests
ATV-310, Add duration param

### Version 2.73.0 (12 May 2023)

ATV-307, Add breakType as Ad tag attr

### Version 2.72.0 (04 May 2023)

ATV-306, Allow any channel for /splash

### Version 2.71.0 (26 April 2023)

ATV-304, Add whatson endpoint

### Version 2.70.0 (25 April 2023)

ATV-305, Change mediaFile type to 'video/mxf'

### Version 2.68.4 (27 March 2023)

ATV-291, fix protocol for additional trackings scripts
ATV-294, imporve Redis integration

### Version 2.68.2 (22 March 2023)

ATV-290, fix default v for get and prefetch

### Version 2.68.1 (06 March 2023)

ATV-290, fix default v for get and prefetch

### Version 2.68.0 (21 February 2023)

ATV-284, add sentry counter

### Version 2.67.2 (17 February 2023)

ATV-282, change separator of deap profiles

### Version 2.67.1 (15 February 2023)

ATV-277, Dodanie mezzanine url do FW response

### Version 2.67.0 (10 February 2023)

ATV-266, Wyłączenie logowania eventów w sentry
ATV-269, Naprawić unlabeled events w sentry
ATV-256, Podłączenie workera pod S3 role connector

### Version 2.66.0 (27 January 2023)

ATV-257, update dependencies

### Version 2.65.2 (07 February 2023)

ATV-274, select redis playlist cache db before getting data

### Version 2.65.1 (01 February 2023)

ATV-243, select redis playlist cache db before getting data

### Version 2.65.0 (20 January 2022)

ATV-243, redis playlist cache
ATV-254, mode next nie działa poprawnie z startBid w okolicach północy

### Version 2.64.2 (12 December 2022)

ATV-249, return correct HTTP code on validation err

### Version 2.64.1 (05 December 2022)

ATV-243, cpu memory profiling, imporve save path

### Version 2.64.0 (22 December 2022)

ATV-249, add logging to HTTP errors
ATV-243, cpu memory profiling

### Version 2.63.0 (20 December 2022)

ATV-247, fix error with Debug tag on non-debug modes

### Version 2.62.0 (15 December 2022)

ATV-244, update node to v.18 and update npm packages

### Version 2.61.1 (20 December 2022)

ATV-247, Poprawka do cache-managera (fix next mode dla prefetch)

### Version 2.61.0 (12 December 2022)

ATV-235, Uruchomienie cache-managera

### Version 2.60.0 (07 December 2022)

TV-240, Poprawka cust_params w trackingu DAI + Mirrored ADS

### Version 2.59.0 (28 November 2022)

ATV-216, Dodanie informacji na temat kampanii do skryptów zliczających DAI
ATV-211, Poprawa HTTP/HTTPS dla Mirrored ads tag Mezzanine/Mediafile

### Version 2.58.0 (16 November 2022)

ATV-200, Improve Redis Connetor
ATV-211, Poprawa HTTP/HTTPS dla Mirrored ads tag Mezzanine/Mediafile
ATV-215, Dodanie informacji na temat duration do skryptów zliczających
ATV-216, Dodanie informacji na temat kampanii do skryptów zliczających DAI
ATV-218, Niespójne działanie startBid
ATV-221, Brak eventów w sentry wysyłanych z adpod-aws
ATV-230, Utworzenie mechanizmu sterującego logowaniem

### Version 2.57.0 (9 November 2022)

ATV-200, Improve Redis Connetor

### Version 2.56.0 (03 November 2022)

ATV-217, Add attr to tracking scripts

### Version 2.55.0 (26 October 2022)

ATV-210, Add Snow logging fixes

### Version 2.54.0 (10 October 2022)

ATV-210, Add Snow logging

### Version 2.53.0 (05 October 2022)

ATV-206, Add v to cust_params

### Version 2.52.3 (26 October 2022)

ATV-219, Niepoprawne działanie next_mixed po wygenerowaniu nowych konfigów

### Version 2.52.2 (14 October 2022)

ATV-214, Add diffCreatives

### Version 2.52.1 (11 October 2022)

ATV-212, Fix multiple renderes & debug mode

### Version 2.52.0 (05 October 2022)

ATV-209, Change param name to advid

### Version 2.51.0 (03 October 2022)

ATV-203, Multiple renderers support

### Version 2.50.0 (30 September 2022)

ATV-204, Fix adServer & tracking scripts requests protocol steup

### Version 2.49.0 (26 September 2022)

ATV-191, Zmiana logiki dla parametru npa
ATV-202, Eliminacja duplikatów oraz nadpisywania tych samych spotów
ATV-199, Błędne ustawianie typu break'u

### Version 2.48.0 (9 September 2022)

ATV-199, Fix breakType attr

### Version 2.47.0 (18 August 2022)

ATV-183, Zmniejszenie logowania do sentry z 100% do 1%
ATV-181, Playlist prefetch startDate fix
ATV-180, Unit testy na Workerze
ATV-178, Kolorowanie logów
ATV-176, Refaktoring cachowania na workerze

### Version 2.46.0 (12 August 2022)

ATV-156, Add exactLength feature fixes

### Version 2.45.0 (28 July 2022)

ATV-179, Zmiana obsługi cust_params w requestach FW

### Version 2.44.0 (25 July 2022)

ATV-156, Add exactLength feature
ATV-168, Poprawa komunikatów walidacji
ATV-172, Allow unknown params
ATV-174, Encode FreeWheel AdRequestUrl userAgent

### Version 2.43.0 (18 July 2022)

ATV-172, enable allow unknown

### Version 2.42.0 (11 July 2022)

ATV-159, Zmiana walidatora w workerze na "Joi"
ATV-161, Dodanie offsetu do każdego prefetch mode
ATV-162, Offset na prefetch nie powinien się dodawać do startDate
ATV-164, Uzupełnienie dokumentacji Swagger na Workerze

### Version 2.41.0 (28 June 2022)

ATV-144, Dodanie nowego mode do prefetch
ATV-145, Dodanie czasu generowania playlisty na get oraz prefetch do logów

### Version 2.40.0 (20 June 2022)

ATV-76, Pass cust_params to orefetch trackings

### Version 2.39.0 (08 June 2022)

ATV-141, Fix workerConfig
ATV-140, Fix next_replaced_debug mode
ATV-138, Add ad_id to impression & change & to /

### Version 2.38.0 (08 June 2022)

ATV-137, Usunięcie raportowania ad-req do Gemius

### Version 2.37.0 (02 June 2022)

ATV-123, Usunięcie raportowania ad-req do Gemius
ATV-107, Unit testy updateAdServerAd.ts
ATV-108, Unit testy createJsonPlaylist.ts
ATV-110, Unit testy setPlaylistResHeaders.ts
ATV-117, Fix URL'a wyświetlanego w Debug dla ADO_BREAK
ATV-120, Fix UNKNOWN_LOG
ATV-127, Poprawki do walidacji eslint
ATV-131, Refactoring pod nową konfigurację eslint
ATV-115, Dodanie do prefetch obsługi mode=nextReplaced

### Version 2.36.2 (24 May 2022)

ATV-118, Fix startBid, update docs

### Version 2.36.1 (23 May 2022)

ATV-116, Fix startBid, fix v param validation

### Version 2.36.0 (19 May 2022)

ATV-83, Zmiana w trackingach dla reklam DAI
ATV-98, Rozszerzenie zadania dodanie +1min w prefetch
ATV-92, Przenieść sortowanie break'ów z Workera do Processora
ATV-102, Fix logowanie błędów
ATV-109, Wydzielenie funkcji log do adpod-tools
ATV-111, Usunąć tryb dynamic na prefetch

### Version 2.35.0 (11 May 2022)

ATV-82, Debug na poziomie bloku rekamowego, debug tylko w VMAP
ATV-43, Integracja z adpod-aws

### Version 2.34.0 (06 May 2022)

ATV-89, W customowych Trackingach wartością param. uadid powinno być adId a nie 'DAI'
ATV-93, Next na prefetchu powinien działać od NOW + 1 min
ATV-90, Dodanie parametru startBid do prefetcha

### Version 2.33.0 (26 April 2022)

ATV-71, Brak uid przekazywane jest jako undefined
ATV-75, Poprawa raportowania trackingów w mirrored ads
ATV-76, Dodanie cust_params do trackingów w mirrored ads

### Version 2.32.0 (14 April 2022)

ATV-15, Node update to v. 17

### Version 2.31.0 (1 April 2022)

ATV-19, Use IAd and IConfiguration from adpod-tools
ATV-37, Rozdzielenie obsługi get i prefetch do osobnych klas, refactoring
ATV-41, Dodanie unit testów do GAMadapter.ts
ATV-45, Dodanie testów do components/image
ATV-46, Dodanie testów co components/info
ATV-47, Dodanie adpod-tools do Jenkins tests tab
ATV-48, Refactoring TS jsonVast4
ATV-55, Użyć xmlParser.ts z adpod-tools w workerze
ATV-59, Refactoring i usprawnienia testów /get
ATV-64, Testy do DASM na workerze
ATV-31, Nowy mechanizm walidacji playlist/get
ATV-57, Utworzenie testów dla prefetch

### Version 2.30.0 (23 March 2022)

ATV-5, Update ADO tracking scripts
ATV-26, Add unit tests
ATV-38, Add unit tests
ATV-39, Add unit tests
ATV-40, Add unit tests
ATV-44, Add startDateToken for prefetch
ATV-27, Remove cacheMirroredAds feature
ATV-49, Improve playlist log
ATV-56, EsLint fixes
ATV-42, Update coverage ignore files list

### Version 2.29.0 (14 March 2022)

ATV-13, Update splash endpoint
ATV-3, One request per break AdOcean integration
ATV-17, Remove output=debug
ATV-24, Improve Errors handling

### Version 2.28.1 (24 March 2022)

ATV-54, Add DASM

### Version 2.28.0 (02 March 2022)

RBD-2254, MirroredAds cache
RBD-2254, DEAP integration
ATV-21, DEAP update fix
ATV-7, Update swagger docs

### Version 2.27.0 (31 January 2022)

RBD-2203, POC device cache
RBD-2204, Update FW request
RBD-2205, Update FW request
RBD-2208, Fix Sentry integration
RBD-2199, Fix breaks order

### Version 2.26.0 (26 January 2022)

RBD-2032, Improve healthcheck
RBD-2243, Update debug mode
RBD-2248, Sort RTL configs
RBD-2235, Skip temp configs download
RBD-2244, Load next day configs before midnight

### Version 2.25.0 (19 January 2022)

RBD-2032, Improve healthcheck

### Version 2.24.0 (14 January 2022)

RBD-2032, Improve healthcheck

### Version 2.23.2 (23 February 2021)

RBD-2334, Improve Prefetch logs; one log per line

### Version 2.23.0 (10 January 2022)

RBD-2167, Update Debug mode
RBD-2168, Mixed mode
RBD-2145, Add FreeWheel integration
RBD-2146, Add FreeWheel integration
RBD-2217, Prefetch response fix
RBD-2215, Update trackings links

### Version 2.22.0 (10 December 2021)

RBD-2087, Fix valitator, revert main.ts

### Version 2.21.0 (10 December 2021)

RBD-2087, Remove adunit support

### Version 2.20.0 (30 November 2021)

RBD-2026, Change ADO request param name

### Version 2.19.0 (08 November 2021)

RBD-1833, Add extra tracking and imporesion scripts to GAM VASTs
RBD-1886, Fix replace all slots ads
RBD-1940, Add timeoffset for VAST4 outputs
RBD-1999, Add bidDate and bidDateToken
RBD-2000, Replace cust_params ADO 'id' with 'ado_id'

### Version 2.18.0 (29 October 2021)

RBD-1885, Fix cache update dates

### Version 2.17.0 (26 October 2021)

RBD-1884, Make startDate optional
RBD-1885, Clear old cache data

### Version 2.16.0 (21 October 2021)

RBD-1927, Add 'adunit' param
RBD-1884, Add startTime to prefetch
RBD-1885, Increase cache size to 2 days

### Version 2.15.0 (13 October 2021)

RBD-1873, Add DEBUG mode to vmap

### Version 2.14.0 (06 October 2021)

RBD-1880, Add NPA to Gemius requests
RBD-1873, Add DEBUG mode
RBD-1883, Remove 'date' param, add cache support limit

### Version 2.13.0 (24 September 2021)

- RBD-1858, Fix ad server URL generator

### Version 2.12.0 (17 September 2021)

- RBD-1765, Remove VAST2 support

### Version 2.11.0 (16 September 2021)

- RBD-1823, Pass ppid in GAM request
- RBD-1824, Pass cust_params in GAM request
- RBD-1799, Swagger update

### Version 2.9.1 (06 September 2021)

- RBD-1798, Pass placement id in ADO request

### Version 2.9.0 (27 August 2021)

- RBD-1733, Add metadata to adslot
- RBD-1745, Add favicon

### Version 2.8.1 (11 August 2021)

- RBD-1712, Fixed image splashing

### Version 2.8.0 (05 August 2021)

- RBD-1669, Rewrite worker to nestjs
- RBD-1630, Added swagger for prefetch
- RBD-1687, Fixed s3 tests
- RBD-1601, Refactored prefetch method

### Version 2.7.1 (26 July 2021)

- RBD-1672, Fixed custom params format

### Version 2.7.0 (14 July 2021)

- RBD-1587, Support custom params

### Version 2.6.0 (28 June 2021)

- RBD-1603, Support Gdpr consent
- RBD-1609, Change http protocol
- RBD-1624, Gdpr consent param change

### Version 2.5.0 (22 June 2021)

- RBD-1514, Increase code tests coverage up to 60%
- RBD-1530, Create GAM connector
- RBD-1550, Support HTTP and HTTPS protocols

### Version 2.4.0 (21 May 2021)

- RBD-1522, Fetch old data by bid and date
- RBD-1519, Change RAND_8 makro to integer only

### Version 2.3.0 (14 May 2021)

- RBD-1509, Fix failed unit tests
- RBD-1486, Added auto-tests config
- RBD-1433, Enable fetching old data

### Version 2.2.1 (12 May 2021)

- RBD-1446, Fix unknown application log when s3 is disconnected

### Version 2.2.0 (10 May 2021)

- RBD-1446, Fix sentry log title
- RBD-1479, Conntect to S3 Bucket

### Version 2.1.3 (23 April 2021)

- RBD-1446, Add repo update timestamp timeout

### Version 2.1.2 (22 April 2021)

- RBD-1446, Fix updating configs all the time in loop

### Version 2.1.1 (16 April 2021)

- RBD-1420, Added wrapper for fetch method with bad status codes handlers.

### Version 2.1.0 (12 April 2021)

- RBD-1264, Add connectors support
- RBD-1327, Added better responses with more specific messages and status codes, adjusted responses to common format, removed logic based on channel/version dictionaries.
- RBD-1397, Add option to fetch archive configs
- RBD-1409, Add new cache logic, removed dictionaries

### Version 2.0.1 (25 March 2021)

- RBD-1378, Fix wrong breakId and timeoffset at prefetch

### Version 2.0.0 (24 March 2021)

- RBD-1359, Get IP from env variable
- RBD-1121, Connect to `Repository service`
- RBD-1122, Connect to `Configuration Generator`
- RBD-1227, Add `Fastify` due to inceasing app performance
- RBD-1374, Add more logs

### Version 1.12.0 (18 March 2021)

- RBD-1358, Assign RTL auto schedule time to environment variables

### Version 1.11.0 (17 March 2021)

- RBD-1355, Change async processor request to sync

### Version 1.10.0 (16 March 2021)

- RBD-1332, Add custom empty vast header due to tracking issues
- RBD-1319, Add non-blocking asking ad server for vasts
- RBD-1352, Timeout request to RTL if it takes too long

### Version 1.9.0 (11 March 2021)

- RBD-1293, Add `adid` transformation
- RBD-1165, Sort breaks ASC for each prefetch mode except next and next_mirrored
- RBD-1321, Add `v1_10_0` transformation

### Version 1.8.0 (03 March 2021)

- RBD-1300, Add `uid` param to base version of configurations

### Version 1.7.0 (24 February 2021)

- RBD-1089, Add integration with transformations for `uid` param from `Configuration Processor`

### Version 1.6.0 (17 February 2021)

- RBD-1089, Add integration with transformations for `copy splitting` from `Configuration Processor`

### Version 1.5.1 (16 February 2021)

- RBD-1272, Rename `TVN_7` to `TVN7`

### Version 1.5.0 (16 February 2021)

- RBD-1155, Add request params to counting codes at mirrored ads and to ad-server links
- RBD-781, Add Eslint
- RBD-1164, Add `TVN Style` and `TVN Turbo` channels
- RBD-1260, Use `MERGE_ID` as configuration BID instead of `ID_SCTE_35`
- RBD-1272, Use google `v1_1_2` transformation in case of ATV ads

### Version 1.4.5 (08 February 2021)

- RBD-1202, Add Sentry info logs, try-catch writing sync files, handling unknwon Sentry tags

### Version 1.4.4 (03 February 2021)

- RBD-1202, Fetching TV configs from DAI only in master worker

### Version 1.4.3 (28 January 2021)

- RBD-1192, Change import TV time to 22:45
- RBD-1197, Add predefinied permanent configs for TTV channel

### Version 1.4.2 (26 January 2021)

- RBD-1186, Add predefinied permanent configs for TTV channel

### Version 1.4.1 (25 January 2021)

- RBD-1171, Disable internal logs and add sorting TV Spots by emission time

### Version 1.4.0 (25 January 2021)

- RBD-1125, Add splashed images for TTV and TVN channels
- RBD-1087, Create ability to returning VASTs created by a given params

### Version 1.3.2 (14 January 2021)

- RBD-1134, Fix a lack of transformations after 11:30 PM

### Version 1.3.1 (12 January 2021)

- RBD-1129, Fix pixel events

### Version 1.3.0 (12 January 2021)

- RBD-1094, Integrate with Configurator Processor application

### Version 1.2.1 (11 January 2021)

- RBD-1119, Turn off logs due to high traffic

### Version 1.2.0 (4 January 2021)

- RBD-1082, Integration with ad server (Gemius AdO)
- RBD-1083, Integration with ad server (Google Ad Manager)

### Version 1.1.0 (29 December 2020)

- RBD-1096, Repair invalid configs cache in version guard
- RBD-1073, Add processor for `replaceable` VAST's attribute
- RBD-1072, Add `ch` and `v` param to VAST Error and Impression tags
- RBD-1074, Create `image/splash` endpoint
- RBD-1060, Add new relic monitoring

### Version 1.0.0 (10 December 2020)

- RBD-679, Create main ads replacement mechanism
- RBD-735, Add `output` param to playlist/create endpoint
- RBD-785, Create conversion to `VMAP` and `VAST 4.0` formats
- RBD-766, Add `timeOffset` and `breakId` args to `VMAP`
- RBD-767, Migrate to AWS environment
- RBD-765, Add `uid` param and tracking headers to ad request
- RBD-789, Create configuration generator
- RBD-799, Write English version of API
- RBD-797, Add deploy param to config generator
- RBD-810, Create random TV Configs generator
- RBD-819, Add tracking events to Ads created by TV Config
- RBD-820, Change ad-request link - add max duration
- RBD-800, Create `mirrored` mode
- RBD-809, Change output parameters to: `VMAP_VAST4` (default), `VAST4`, `DEBUG` (Vast 2.0)
- RBD-845, Fix load tv config overlap error
- RBD-802, Change spots format to `ATV` and `TV`
- RBD-840, Add two specific configs for Nowtilus company
- RBD-831, Add Error tags to VAST
- RBD-830, Create spots prefetching: `24h_mirrored`, `next` and `next_mirrored`
- RBD-861, Connect to TVN Schedule Repository
- RBD-891, Add specifis AdPod configs for S&T company
- RBD-864, Automatic import tommorow's tv configs
- RBD-883, Add a few options to prefetch: `30m`, `30m_mirrored`, `1h`, `1h_mirrored`, `dynamic` and `dynamic_mirrored`
- RBD-902, Divide playlist/get endpoint into playlist/get and playlist/prefetch endpoints
- RBD-914, Create AdPod ability to operate in multi threads simultaneously
- RBD-934, Add ability to convert tvTime to adPod time (without miliseconds) during convertion tv config to adpod config. Keep also ability to operate with tv configs containing ad pod time
- RBD-907, Invalid output param during prefetch is treated like default param
- RBD-901, Add emissionTime attribiute to prefetched VASTs
- RBD-963, Docker should use all available CPUs
- RBD-971, Add AdPod config for Nowtilus company
- RBD-951, Automatic importConfigs after docker container start
- RBD-937, Fix app hang during saving configurations process
- RBD-987, Add check endpoint
- RBD-988, Delete useless adserver requests during mirrored call
- RBD-989, Create AdPod configurations Cache System
- RBD-863, Add multi channels operability: `TEST`, `TTV`, `TVN` and `TVN_7`
- RBD-1030, Add prefetch guard, fix time zone conversion
- RBD-1042, Protect before invalid tv schedule, fix invalid autoSchedule date
- RBD-1048, Connect AdPod to Sentry
- RBD-994, Config all date filter add
- RBD-1053, Fix wrong TV Schedule request for TVN 7 channel
- RBD-1054, Fix TV schedule response for entire day
- RBD-995, Add filtering configs by HMS time
