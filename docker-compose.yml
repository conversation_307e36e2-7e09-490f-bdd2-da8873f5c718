version: '3.8'

volumes:
  s3-data:

services:
  redis-cluster:
    image: docker.io/bitnami/redis-cluster:8.0
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_CLUSTER_REPLICAS=0
      - 'REDIS_NODES=127.0.0.1 127.0.0.1 127.0.0.1'
      - REDIS_CLUSTER_CREATOR=yes
      - REDIS_CLUSTER_DYNAMIC_IPS=no
      - REDIS_CLUSTER_ANNOUNCE_IP=redis.local.gd
    ports:
      - '6379-6381:6379-6381'

  s3:
    image: quay.io/minio/minio
    command: minio server --console-address ":9001" /data
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - s3-data:/s3-data
    environment:
      - MINIO_ROOT_USER=fake-admin
      - MINIO_ROOT_PASSWORD=fake-secret123
      - MINIO_DOMAIN=s3.local.gd
    restart: always

  mc-cli:
    image: minio/mc
    depends_on:
      - s3
    entrypoint: >
      /bin/sh -c "
      sleep 5;
      /usr/bin/mc alias set dockerminio http://s3:9000 fake-admin fake-secret123 && \
      /usr/bin/mc mb dockerminio/adpod-test-bucket && \
      /usr/bin/mc anonymous set public dockerminio/adpod-test-bucket && \
      touch /tmp/initialized && \
      tail -f /dev/null;
      "

  test-services:
    image: node:22.15.0
    depends_on:
      redis-cluster:
        condition: service_started
      s3:
        condition: service_started
      mc-cli:
        condition: service_started
